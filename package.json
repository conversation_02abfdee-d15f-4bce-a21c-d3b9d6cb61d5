{"name": "lock-front", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "npm run dev", "dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@mui/material": "^6.1.3", "@mui/x-date-pickers": "^7.20.0", "@react-oauth/google": "^0.12.1", "@reduxjs/toolkit": "^2.2.5", "@types/swiper": "^6.0.0", "axios": "^1.7.2", "date-fns": "^4.1.0", "i18next": "^23.15.1", "i18next-browser-languagedetector": "^8.0.0", "i18next-http-backend": "^2.6.1", "leaflet": "^1.9.4", "leaflet-geosearch": "^4.0.0", "leaflet.markercluster": "^1.5.3", "lodash": "^4.17.21", "react": "^18.2.0", "react-calendar": "^5.0.0", "react-date-range": "^2.0.1", "react-datepicker": "^7.3.0", "react-dom": "^18.2.0", "react-hook-form": "^7.51.5", "react-i18next": "^15.0.1", "react-icons": "^5.2.1", "react-infinite-scroll-component": "^6.1.0", "react-leaflet": "^4.2.1", "react-phone-number-input": "^3.4.3", "react-redux": "^9.1.2", "react-router-dom": "^6.23.1", "react-toastify": "^10.0.5", "react-use-websocket": "^4.8.1", "swiper": "^11.1.12", "vite-tsconfig-paths": "^4.3.2"}, "devDependencies": {"@eslint/js": "^9.9.0", "@types/eslint__js": "^8.42.3", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "postcss": "^8.4.38", "tailwindcss": "^3.4.3", "typescript": "^5.5.4", "typescript-eslint": "^8.2.0", "vite": "^5.2.0"}}