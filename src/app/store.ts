import { combineReducers, configureStore } from "@reduxjs/toolkit";
import authReducer from 'store/slices/auth/AuthSlice.js'
import notificationReducer from "features/notification/model/notificationSlice.js";
import profileReducer from "features/profile/model/ProfileSlice.js";
import { handbookApi } from "store/slices/handbook/handbookSlice";
import accommodationReducer from "../store/slices/accommodation/accommodationSlice";
import bookingReducer from "../store/slices/booking/BookingSlice"
import lockKeyReducer from "../store/slices/lock/lockSlice"
import adminListReducer from "../store/slices/admin/adminListSlice"
import adminEntityReducer from "../store/slices/admin/adminEntitySlice"
import uiReducer from 'store/slices/uiSlice/uiSlice';


const rootReducer = combineReducers({
    auth: authReducer,
    notifications: notificationReducer,
    profile: profileReducer,
    accommodations : accommodationReducer,
    booking: bookingReducer,
    lockKeys: lockKeyReducer,
    admin: adminListReducer,
    adminEntities: adminEntityReducer,
    ui: uiReducer,
    [handbookApi.reducerPath]: handbookApi.reducer
})

const store = configureStore({
    reducer: rootReducer,
    middleware: (getDefaultMiddleware) => {
        return getDefaultMiddleware({
            serializableCheck: {
                ignoredActions: ['POP']
            }
        }).concat(handbookApi.middleware);
    }
});
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export default store;