import BaseLayout from "widgets/layouts/BaseLayout";
import Router from "page/Router";
import { Route, Routes } from "react-router-dom";
import AuthRouter from "page/authRoutes/AuthRouter";
import AuthLayout from "widgets/layouts/AuthLayout";
import UseScrollTop from "../../shared/hooks/useScrollTop.js";
import AdminLayout from "../../widgets/layouts/AdminLayout.jsx";
import AdminRouter from "../../page/moderator/AdminRouter.jsx";

const RouterProvider = () => {

    return (
        <>
            <UseScrollTop/>
            <Routes>
                <Route path="/auth/*" element={<AuthLayout><AuthRouter /></AuthLayout>} />
                <Route path="/admin/*" element={<AdminLayout><AdminRouter /></AdminLayout>} />
                <Route path="/*" element={<BaseLayout>
                    <Router />
                </BaseLayout>} />
            </Routes></>
    )
}

export default RouterProvider;