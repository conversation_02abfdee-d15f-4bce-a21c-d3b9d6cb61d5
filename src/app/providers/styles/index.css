@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;


body {
    font-family: "Montserrat", sans-serif;
    text-decoration: none;
}

li {
    text-decoration: none;
    list-style-type: none;
    /* Remove bullets */
    padding: 0;
    /* Remove padding */
    margin: 0;
    /* Remove margins */
}

@layer base {
    input[type="number"]::-webkit-inner-spin-button,
    input[type="number"]::-webkit-outer-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }
}


input:focus,
input:focus:after,
input:focus:before {
    outline: none;
    box-shadow: none;
}

.YourClassName .PhoneInput input:focus,
.YourClassName .PhoneInput input:focus:after,
.YourClassName .PhoneInput input:focus:before {
    outline: none;
    box-shadow: none;
}



.custom-scrollbar {
    scrollbar-width: thin; /* Для Firefox */
    scrollbar-color: #2d5069 #ffffff; /* Цвет бегунка и фона */
}

.custom-scrollbar::-webkit-scrollbar {
    width: 8px; /* Ширина скроллбара */
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #e0e0e0; /* Фон трека */
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: #1f3747; /* Цвет бегунка */
    border-radius: 10px; /* Скругленные края */
    border: 2px solid #e0e0e0; /* Обводка вокруг бегунка */
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #555; /* Цвет бегунка при наведении */
}

.marker-cluster {
    color: #fff; /* Белый текст */
}

.marker-cluster div {
    background-color: rgba(0, 123, 255, 0.6);
}




/* .leaflet-container {
    height: 600px;
    width: 90%;
    border-radius: 2rem;
} */