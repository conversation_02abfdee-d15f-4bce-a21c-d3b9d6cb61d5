import { Browser<PERSON>outer } from "react-router-dom";
import { I18nextProvider } from 'react-i18next';
import i18n from '/i18n.js';
import GoogleProvider from "./GoogleOAuthProviver";
import RouterProvider from "./RouterProvider";
import StoreProvider from "./StoreProvider";
import ToastProvider from "./ToastProvider";

//styles
import 'react-toastify/dist/ReactToastify.css';
import './styles/index.css';

const Provider = () => {

    return (
        <StoreProvider>
            <I18nextProvider i18n={i18n}>
                <BrowserRouter>
                    <GoogleProvider>
                        <RouterProvider />
                    </GoogleProvider>
                    <ToastProvider />
                </BrowserRouter>
            </I18nextProvider>
        </StoreProvider>
    );
}

export default Provider;
