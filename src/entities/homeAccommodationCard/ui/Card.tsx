import React from 'react'
import { FaStar } from "react-icons/fa";
import Slider from 'shared/ui/slider/Slider';
import {Link} from "react-router-dom";
import {CardProps} from "../model/types";
import {truncate} from "../../../shared/utils/truncate/truncate";



const Cards: React.FC<CardProps> = ({item, images}) => {

  return (
    <div className='w-full'>
        <Slider
            style={{display: "flex"}}
            images={images}
            ImageByBool={true}
            items={[]}
            link={`accommodation/${item.ID}`}
            arrowClassName="z-0"
        />
        <Link to={`accommodation/${item.ID}`}>
            <div className='flex flex-col gap-1'>
                <div className='flex justify-between pt-2'>
                    <p className='text-xl font-semibold text-blue-lock'>{item.Price} сом</p>
                    <ul className='flex gap-1 items-center'>
                        <span className='text-yellow-400'><FaStar/></span>
                        <li className='text-md font-light'>{item.Rating}</li>
                    </ul>
                </div>
                <p className=' text-xl font-semibold text-blue-text-lock'>{truncate(item.Title, 45)}</p>
                <p className='flex items-center'>
                    <img src={`/location.svg`}
                         alt="img"
                         className="inline-block w-3 h-3 mr-1"
                    />
                    <span className='text-sm text-light-text'>{item.LocationLabel}</span>
                </p>
                <p className='text-sm text-light-text'>{item.Text}</p>
                <p className='text-sm text-light-text'>{truncate(item?.Description, 100)}</p>
            </div>
        </Link>
    </div>
  )
}
export default Cards;
