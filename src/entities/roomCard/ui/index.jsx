import React, { useState } from 'react';
import { Link } from "react-router-dom";
import { API_IMG } from "../../../shared/config/api.js";
import noImg from "../../../../public/noImg.jpg";
import Modal from "../../../shared/ui/modal/Modal.jsx";
import {truncate} from "../../../shared/utils/truncate/truncate.js";
import {useTranslation} from "react-i18next";

function RoomsCard({
                       room,
                       onEdit = () => console.log("clicked"),
                       onRemove = () => console.log("clicked"),
                       onPublish = () => console.log("clicked")
                   }) {

    const [isModalOpen, setIsModalOpen] = useState(false);
    const {t} = useTranslation()


    const handleDeleteClick = () => {
        setIsModalOpen(true);
    };

    const handleConfirmDelete = () => {
        onRemove();
        setIsModalOpen(false);
    };

    const handleCancelDelete = () => {
        setIsModalOpen(false);
    };


    return (
        <div className={`border rounded-lg overflow-hidden shadow-lg mb-4 w-full relative`} key={room?.ID}>
            <Link to={`/accommodation/${room.ID}`} className='cursor-pointer'>
                <img src={room?.Images?.length > 0 ? `${API_IMG}${room?.Images?.[0]?.ImageUrl}` : noImg}
                     alt={room?.Title}
                     className='w-full h-60 object-cover'/>
                <div className='px-4 pt-4'>
                    <h3 className='text-md md:text-xl font-semibold mb-2'>{room?.Title}</h3>
                    <p className='text-gray-700 mb-2'>{truncate(room?.Description, 33)}</p>
                </div>
            </Link>
            {room.Verification === "pending" && (
                <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 text-white text-lg font-semibold">
                    На рассмотрении
                </div>
            )}


            {room.Verification === "verified" && (
                <div className="flex px-4 pb-4 justify-between">
                    <div className={`flex items-center justify-center mt-2 p-2 ${room.IsAvailable ? 'bg-btn-border-blue' : 'bg-text-light-red'} rounded-full`}>
                        <label className="relative inline-flex items-center cursor-pointer">
                            <input
                                type="checkbox"
                                checked={room?.IsAvailable}
                                onChange={(e) => {
                                    e.preventDefault();
                                    onPublish(e.target.checked);
                                }}
                                className="sr-only peer"
                            />
                            <div
                                className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-0 peer-focus:ring-red-500 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-white peer-checked:after:bg-blue-500 after:bg-text-light-red"></div>
                            <span className={`ml-3 text-sm font-semibold text-white`}>{room?.IsAvailable ? t('common.active') : t('common.disabled')}</span>
                        </label>
                    </div>

                    <div className='flex gap-4'>
                        <button onClick={onEdit} className="">
                            <img
                                src="/editIcon.svg" // Путь к SVG иконке редактирования
                                alt="Edit"
                                className="w-6 h-6"
                            />
                        </button>
                        <button onClick={handleDeleteClick} className="">
                            <img
                                src="/deleteIcon.svg" // Путь к SVG иконке удаления
                                alt="Delete"
                                className="w-6 h-6"
                            />
                        </button>

                    </div>
                </div>
            )}

            <Modal
                isOpen={isModalOpen}
                onClose={handleCancelDelete}
                onConfirm={handleConfirmDelete}
                confirmButtonProps={{
                    className: 'w-full px-4 py-2 bg-blue-500 text-white rounded-md cursor-pointer'
                }}
                title="Удаление объекта"
                confirmText={t('button.delete')}
                cancelText={t('button.cancel')}
                style="max-w-md"
            >
                <div className='flex flex-col items-center justify-center my-5'>
                    <p className='font-semibold'>Вы точно хотите удалить объявление?</p>
                    <p className='font-semibold text-text-light-red text-xl'>{room?.Title}</p>
                </div>
            </Modal>
        </div>
    );
}

export default RoomsCard;
