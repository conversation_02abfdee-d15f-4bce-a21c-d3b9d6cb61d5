import React, {useEffect} from 'react';
import Button from "shared/ui/button/Button.jsx";
import { API_IMG } from "../../../shared/config/api.js";
import { Link } from "react-router-dom";
import {useDispatch, useSelector} from "react-redux";
import {getAccommodationByID} from "../../../store/actions/accommodations/Accommodation";
import {getUserInfo} from "../../../store/actions/auth/Auth.js";
import {calculateDays} from "../../../shared/utils/calculateDays/calculateDays.js";
import UserImage from "shared/assets/user.png";
import {formatDate} from "../../../shared/utils/formatDate/index.js";
import UserCard from "../../userCard/ui/index.jsx";
import {useTranslation} from "react-i18next";
import Spinner from "shared/ui/spinner/Spinner.jsx";

function ApplicantsCard({
    item,
    onConfirm,
    onReject,
    loadingReject,
    loadingConfirm
}){
    const dispatch = useDispatch()
    const {t} = useTranslation()
    const user = useSelector(state => state.auth?.userInfo[item.UserID]);
    const accommodation = useSelector(state => state.accommodations.accommodationByID[item.AccommodationID]?.Accommodation);

    useEffect(() => {
        if (!accommodation) {
            dispatch(getAccommodationByID(item.AccommodationID));
        }
        if (!user) {
            dispatch(getUserInfo(item.UserID));
        }
    }, [dispatch, item.AccommodationID, item.UserID, accommodation, user]);

    const daysCount = calculateDays(item.StartDate, item.EndDate);


    return (
        <div key={item.ID} className='flex flex-col border border-gray-300 rounded-lg p-4 '>
            <h2 className="text-lg font-semibold pb-2 mb-2 border-b">
                {accommodation ? (
                    <Link to={`/accommodation/${accommodation.ID}`}>
                        {accommodation.Title}
                    </Link>
                ) : (
                    <span>Загрузка...</span>
                )}
            </h2>
            <div className='border-b pb-5 space-y-2'>
                <p className='flex justify-between font-medium text-light-text'>
                    <span className=''>{t('applicationCard.applicationDateAndTime')}</span>
                    <span>{formatDate(item.CreatedAt)}</span>
                </p>
                <p className='flex justify-between font-medium text-light-text'>
                    <span className=''>{t('applicationCard.amount')}:</span>
                    <span>{item.TotalSum}</span>
                </p>
                <p className='flex justify-between font-medium text-light-text'>
                    <span>{t('applicationCard.numberOfDays')}:</span>
                    <span>{daysCount}</span>
                </p>
                <p className='flex justify-between font-medium text-light-text'>
                    <span>{t('applicationCard.numberOfGuests')}:</span>
                    <span>{item.PeopleQuantity}</span>
                </p>
                <p className='flex justify-between font-medium text-light-text'>
                    <span>{t('applicationCard.arrivalDate')}:</span>
                    <span>{item.StartDate}</span>
                </p>
                <p className='flex justify-between font-medium text-light-text'>
                    <span>{t('applicationCard.departureDate')}:</span>
                    <span>{item.EndDate}</span>
                </p>
            </div>
            {user && (
                <div className='pt-5'>
                    <div className='flex justify-between'>
                        <UserCard
                            title='applicationCard.client'
                            item={user}
                            id={item.UserID}
                        />
                        <div>
                            <p className='flex flex-col '>
                                <span className='font-medium text-light-text'>{t('applicationCard.status')}:</span>
                                <span
                                    className={ item.Status === 'pending' ? 'text-blue-text-lockKey font-semibold' : item.Status === 'active' ? 'text-green-500 font-semibold' : 'text-red-600 font-semibold'}
                                >{item.Status === 'pending' ? t('status.pending') : item.Status === 'active' ? t('status.active') : t('status.rejected')}</span>
                            </p>
                        </div>
                    </div>

                </div>
            )}
            {item.Status !== 'rejected' && item.Status !== 'active' && (
                <div className='flex gap-5 mt-2 '>
                    <Button
                        name={loadingConfirm[item.ID] ?  null : t('button.confirm') }
                        onClick={() => onConfirm(item.ID)}
                        style='w-full px-5 py-2 bg-btn-blue text-white font-semibold'
                        iconComponent={loadingConfirm[item.ID] ? <Spinner /> : null}
                        />
                    <Button
                        name={t('button.reject')}
                        onClick={() => onReject(item.ID)}
                        style='w-full px-5 py-2 text-red-500 font-semibold'

                    />
                </div>
            )}
        </div>
    );
}

export default ApplicantsCard;
