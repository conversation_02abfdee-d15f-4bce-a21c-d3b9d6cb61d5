import React from 'react';
import {API_IMG} from "shared/config/api.js";
import UserImage from "shared/assets/user.png";
import {useTranslation} from "react-i18next";
import {Link} from "react-router-dom";

function UserCard({title, item, avatarClassName, id}) {
    const {t} = useTranslation()
    return (
        <div className='flex flex-col w-max'>
            <Link to={`/userview/${id}`}>
                <p className='font-semibold mb-2 lg:text-lg'>{t(title)}</p>
                <div className='flex items-center gap-4 cursor-pointer w-max'>
                    <img src={item.Avatar ? API_IMG + item.Avatar : UserImage} alt='img'
                         className={`${avatarClassName} w-10 h-10 rounded-full mb-2`}/>
                    <p className='font-bold text-light-text'>{item.Nickname ? item.Nickname : 'no nickname'}</p>
                </div>
            </Link>
        </div>
    );
}

export default UserCard;

