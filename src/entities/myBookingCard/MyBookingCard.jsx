import {API_IMG} from "../../shared/config/api.js";
import {Link} from "react-router-dom";
import {useDispatch, useSelector} from "react-redux";
import React, {useEffect} from "react";
import {getAccommodationByID} from "../../store/actions/accommodations/Accommodation.ts";
import {getUserInfo} from "../../store/actions/auth/Auth.js";
import UserImage from "shared/assets/user.png";
import {calculateDays} from "../../shared/utils/calculateDays/calculateDays.js";
import {formatDate} from "../../shared/utils/formatDate/index.js";
import UserCard from "../userCard/ui/index.jsx";
import {useTranslation} from "react-i18next";


function MyBookingCard({ booking,OwnerID }) {
    const {t} = useTranslation()

    const dispatch = useDispatch()
    const accommodation = useSelector(state => state.accommodations?.accommodationByID[booking.AccommodationID]?.Accommodation);
    const owner = useSelector(state => state.auth?.userInfo[accommodation?.OwnerID]);

    useEffect(() => {
        if (!accommodation) {
            dispatch(getAccommodationByID(booking.AccommodationID));
        }
    }, [dispatch, booking.AccommodationID, accommodation]);

    useEffect(() => {
        if (accommodation && !owner) {
            dispatch(getUserInfo(accommodation.OwnerID));
        }
    }, [dispatch, accommodation, owner]);


    return (
        <div key={booking.ID} className='border border-gray-300 rounded-lg p-4'>
            <h3 className="text-lg font-semibold pb-2 mb-2 border-b">
                <Link to={`/accommodation/${accommodation?.ID}`}>
                    {accommodation?.Title}
                </Link>
            </h3>
            <div className='border-b pb-5 space-y-2'>
                <p className='flex justify-between font-medium text-light-text'>
                    <span>{t('applicationCard.amount')}:</span>
                    <span>{booking.TotalSum} {t('applicationCard.currency')}</span>
                </p>
                <p className='flex justify-between font-medium text-light-text'>
                    <span>{t('applicationCard.numberOfDays')}:</span>
                    <span>{booking.Days}</span>
                </p>
                <p className='flex justify-between font-medium text-light-text'>
                    <span>{t('applicationCard.numberOfGuests')}:</span>
                    <span>{booking.PeopleQuantity}</span>
                </p>
                <p className='flex justify-between font-medium text-light-text'>
                    <span>{t('applicationCard.arrivalDate')}: </span>
                    <span>{booking.StartDate}</span>
                </p>
                <p className='flex justify-between font-medium text-light-text'>
                    <span>{t('applicationCard.departureDate')}:</span>
                    <span>{booking.EndDate}</span>
                </p>
                <p className='flex justify-between font-medium text-light-text'>
                    <span>{t('applicationCard.bookingTime')}:</span>
                    <span>{formatDate(booking.CreatedAt)}</span>
                </p>
            </div>

            {owner && (
                <div className='pt-5'>
                    <div className='flex justify-between'>
                        <UserCard
                            title='role.owner'
                            item={owner}
                            id={OwnerID}
                        />
                        <div>
                            <p className='flex flex-col '>
                                <span className='font-medium text-light-text'>{t('applicationCard.status')}:</span>
                                <span
                                    className={booking.Status === 'pending' ? 'text-blue-text-lockKey font-semibold' : booking.Status === 'active' ? 'text-green-500 font-semibold' : 'text-red-600 font-semibold'}
                                >{booking.Status === 'pending' ? t('status.pending') : booking.Status === 'active' ? t('status.active') : t('status.rejected')}</span>
                            </p>
                        </div>
                    </div>
                </div>
            )}
            {
                booking.Status === 'active' && (
                    <div className='flex flex-col items-center border-t pt-4'>
                        <p className='uppercase text-text-medium font-semibold'>Код замка</p>
                        <span className='text-xl md:text-3xl text-text-light-red font-semibold'>{booking.KeyboardPwd}</span>
                    </div>
                )
            }
            {
                booking.Status === 'rejected' && (
                    <div className='flex pt-5 border-t gap-2'>
                        <span className='text-blue-text-lock font-semibold'>Причина отказа:</span>
                        <span>{booking.RejectionReason}</span>
                    </div>
                )
            }
        </div>
    );
}

export default MyBookingCard;
