import React from 'react';
import {getTextColorClass} from "../../shared/utils/textColorClass/getTextColorClass.js";
import {SlClock} from "react-icons/sl";
import {formatDate} from "../../shared/utils/formatDate/index.js";

function NotificationList({ notifications, onNotificationClick }) {
    return (
        <div className=' py-2 md:py-5'>
            {notifications.map((item, index) => (
                <div
                    className={`flex flex-col md:flex-row gap-2 md:gap-5 py-3 md:p-3 md:items-center border-b border-gray-300 md:hover:bg-gray-200 cursor-pointer ${item.IsRead ? 'bg-white' : 'bg-gray-200 hover:none'}`}
                    key={index}
                    onClick={() => onNotificationClick(item)}
                >
                    <div className='flex-1 flex-col'>
                        <div className='flex gap-2'>
                            <p className={`px-2 text-xs md:text-sm w-max rounded-md text-white bg-green-400 ${getTextColorClass(item.Notification.Title)}`}>
                                {item.Notification.Title}
                            </p>
                            <span
                                className={`text-xs md:text-sm ${item.IsRead ? 'text-green-500' : 'text-red-500'}`}>
                                {item.IsRead ? 'Прочитано' : 'Не прочитано'}
                            </span>
                        </div>
                        <p className='mt-2 text-sm md:text-md'>{item.Notification.Text}</p>
                    </div>
                    <div className='flex items-center gap-2'>
                        <span className='text-sm text-gray-400'><SlClock/></span>
                        <span className='text-xs md:text-sm text-gray-400'>{formatDate(item.CreatedAt)}</span>
                    </div>
                </div>
            ))}
        </div>
    );
}

export default NotificationList;