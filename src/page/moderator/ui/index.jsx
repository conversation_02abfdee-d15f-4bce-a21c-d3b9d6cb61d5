import React from 'react';
import NavBar from "../../../widgets/admin/navBar/ui/index.jsx";
import SideBar from "../../../widgets/admin/sideBar/ui/index.jsx";
import {Outlet} from "react-router-dom";

function AdminPanel(props) {
    return (
        <div className="admin-panel h-screen flex flex-col ">
            <NavBar/>
            <div className="admin-content flex flex-1 ">
                <SideBar/>
                <div className="content-area flex-1 p-6 ">
                    <Outlet />
                </div>
            </div>
        </div>
    );
}

export default AdminPanel;