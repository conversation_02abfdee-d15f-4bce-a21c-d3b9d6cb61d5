import { Route, Routes } from "react-router-dom";
import AdminPanel from "./ui/index.jsx";
import UserList from "../../widgets/admin/user/ui/user.jsx";
import AccommodationListAdmin from "../../widgets/admin/accommodation/ui/index.jsx";
import ProtectedRoute from "../../shared/libs/routing/ProtectedRoute.jsx";
import BookingsAdmin from "../../widgets/admin/bookings/index.jsx";
import LockKeys from "../../widgets/admin/lockKey/index.jsx";
import CategoryAdmins from "../../widgets/admin/category/index.jsx";
import FacilityAdmins from "../../widgets/admin/facility/index.jsx";
import RulesAdmins from "../../widgets/admin/rules/index.jsx";
import ApplicationsAdmins from "../../widgets/admin/applications/index.jsx";
import ApplicationDetail from "../../widgets/admin/applications/ui/ApplicationDetail.jsx";
import UserDetail from "../../widgets/admin/applications/ui/UserDetail.jsx";
import UserDeleteRequestDetails from "../../widgets/admin/applications/ui/UserDeleteRequestDetails.jsx";

const AdminRouter = () => {
    return (
        <Routes>
            {/* Защищенный маршрут для всего раздела admin */}
            <Route element={<ProtectedRoute />}>
                <Route path="/" element={<AdminPanel />}>
                    <Route path="users" element={<UserList />} />
                    <Route path="accommodation" element={<AccommodationListAdmin />} />
                    <Route path="bookings" element={<BookingsAdmin />} />
                    <Route path="locks" element={<LockKeys />} />
                    <Route path="dictionary/categories" element={<CategoryAdmins />} />
                    <Route path="dictionary/facility" element={<FacilityAdmins />} />
                    <Route path="dictionary/rules" element={<RulesAdmins />} />
                    <Route path="applications" element={<ApplicationsAdmins />} />
                    <Route path="applications/show/:id" element={<ApplicationDetail />} />
                    <Route path="applications/user/show/:id" element={<UserDetail />} />
                    <Route path="applications/user/delete-request/:id" element={<UserDeleteRequestDetails />} />
                    <Route path="*" element={<>Not found</>} />
                </Route>
            </Route>
        </Routes>
    );
}

export default AdminRouter;
