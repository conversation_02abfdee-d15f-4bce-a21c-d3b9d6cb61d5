import React, {useState, useMemo, useEffect} from "react";
import { useDispatch } from "react-redux";
import { createNewBooking } from "store/actions/booking/Booking";
import { useNavigate, useLocation } from "react-router-dom";
import Button from "shared/ui/button/Button";
import { API_IMG } from "shared/config/api";
import noImg from "/public/noImg.jpg";
import { useSelector } from "react-redux";
import {useTranslation} from "react-i18next";
const ReservationPage: React.FC = () => {
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const {t} = useTranslation()
    const profile = useSelector((state) => state.auth.user?.profile);

    const { state } = useLocation();
    const room = state?.room; 
    const [name, setName] = useState(profile?.Name || "");
    const [surname, setSurname] = useState(profile?.Surname || "");
    const [phoneNumber, setPhoneNumber] = useState(profile?.PhoneNumber || "");
    const [checkInDate, setCheckInDate] = useState(state?.checkInDate || "");
    const [checkOutDate, setCheckOutDate] = useState(state?.checkOutDate || "");
    const [guestCount, setGuestCount] = useState(state?.guestCount || 1);
    const [childrenCount, setChildrenCount] = useState(state?.childrenCount || 0);
    const [message,setMessage] = useState("")
    const isAuthenticated= useSelector(state => state.auth.isAuthenticated)


    useEffect(() => {
        if(!isAuthenticated){
            navigate('/')
        }
    }, [isAuthenticated]);

    const calculateTotalNights = () => {
        if (checkInDate && checkOutDate) {
            const checkIn = new Date(checkInDate);
            const checkOut = new Date(checkOutDate);
            const diffTime = Math.abs(checkOut - checkIn);
            return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        }
        return 0;
    };

    const calculateTotalCost = () => {
        return totalNights * (room?.Price || 0);
    };

    const totalNights = useMemo(() => calculateTotalNights(), [checkInDate, checkOutDate]);
    const totalCost = useMemo(() => calculateTotalCost(), [totalNights, room?.Price]);

    const handleBooking = () => {
        const bookingData = {
            Name: name,
            Surname: surname,
            PhoneNumber: phoneNumber,
            AccommodationID: room.ID, 
            StartDate: checkInDate,
            EndDate: checkOutDate,
            PeopleQuantity: parseInt(guestCount, 10),
            Days: totalNights,
            TotalSum: totalCost,
            Message: message
           };
        dispatch(createNewBooking(bookingData))
            .then(() => {
                navigate('/profile/my-bookings');
            })
            .catch((error) => {
                console.error("Ошибка при создании бронирования:", error);
            });
    };

    const imageUrl = room.Images?.[0]?.ImageUrl ? `${API_IMG}${room.Images[0].ImageUrl}` : noImg;
    const isBookingDisabled = !checkInDate || !checkOutDate;
    return ( 
        <div className="container mx-auto md:p-4">
                {/* <button
                    className="bg-transparent text-gray-800 py-2 px-4 rounded "
                    onClick={() => navigate(-1)}
                >
                    {'<--'} Вернуться
                </button> */}
            <div className="flex justify-center ">
                <h2 className="text-xl font-semibold mb-6">{t('bookingPage.bookingDetails')}</h2>
            </div>
            <div className='mx-auto grid lg:grid-cols-3 gap-4 lg:gap-10'>
                <div className="col-span-2 border shadow-xl rounded-2xl p-5 ">
                    <h2 className="text-xl font-semibold mb-6">{room.Title}</h2>
                    <div className="grid grid-cols-1 lg:grid-cols-5 gap-4 lg:gap-10">
                        {/* Картинка */}
                        <div
                            className="relative w-full h-[300px] lg:h-[200px] overflow-hidden rounded-2xl mb-4 lg:order-last col-span-2 lg:col-span-2">
                            <img
                                src={imageUrl}
                                alt={`${room.Title} main photo`}
                                className="absolute inset-0 w-full h-full object-cover"
                            />
                            <div className="absolute inset-0 bg-black/30 flex items-end justify-center rounded-lg">
                                <p className="text-white md:text-sm font-bold m-1">{room.LocationLabel}</p>
                            </div>
                        </div>
                        {/* Инпуты */}
                        <div className="grid grid-cols-2 col-span-3 lg:col-span-3 grid-rows-2 gap-4 lg:order-first">
                            <div className="mb-4">
                                <label className="block text-gray-700 font-normal">{t('bookingPage.arrival')} <span
                                    className="text-red-500">*</span></label>
                                <input
                                    required
                                    type="date"
                                    value={checkInDate}
                                    onChange={(e) => setCheckInDate(e.target.value)}
                                    className="w-full border-b border-b-gray-300 px-2 py-1 focus:border-blue-500"
                                />
                            </div>
                            <div className="mb-4">
                                <label className="block text-gray-700 font-normal">{t('bookingPage.departure')} <span
                                    className="text-red-500">*</span></label>
                                <input
                                    required
                                    type="date"
                                    value={checkOutDate}
                                    onChange={(e) => setCheckOutDate(e.target.value)}
                                    className="w-full border-b border-b-gray-300 px-2 py-1 focus:border-blue-500"
                                />
                            </div>
                            <div className="mb-4">
                                <label className="block text-gray-700 font-normal">{t('bookingPage.adults')} <span
                                    className="text-red-500">*</span></label>
                                <select
                                    required
                                    className="w-full border-b border-b-gray-300 px-2 py-1 focus:border-blue-500"
                                    value={guestCount}
                                    onChange={(e) => setGuestCount(e.target.value)}
                                >
                                    {Array.from({length: room?.PeopleQuantity || 10}, (_, i) => (
                                        <option key={i + 1} value={i + 1}>
                                            {i + 1}
                                        </option>
                                    ))}
                                </select>
                            </div>
                            <div className="mb-4">
                                <label className="block text-gray-700 font-normal">{t('bookingPage.children')} <span
                                    className="text-red-500">*</span></label>
                                <select
                                    required
                                    className="w-full border-b border-b-gray-300 px-2 py-1 focus:border-blue-500"
                                    value={childrenCount}
                                    onChange={(e) => setChildrenCount(e.target.value)}
                                >
                                    {Array.from({length: room?.PeopleQuantity || 10}, (_, i) => (
                                        <option key={i + 1} value={i + 1}>
                                            {i + 1}
                                        </option>
                                    ))}
                                </select>
                            </div>
                        </div>
                    </div>

                    <h3 className="font-semibold mb-4">{t('bookingBar.note')} :</h3>
                    <div className="mb-5">
                        {room.Rules.map((rule) => (
                            <div key={rule.ID} className="flex items-center mb-2">
                                 <span
                                     style={{
                                         display: 'inline-block',
                                         width: '8px',
                                         height: '8px',
                                         backgroundColor: '#DE4F65',
                                         borderRadius: '50%',
                                         marginRight: '8px'
                                     }}
                                 ></span>
                                <p className="text-red-600 font-light text-sm ">{rule.Value}</p>
                            </div>
                        ))}
                    </div>
                    <h3 className="text-xl font-semibold mb-6">{t('bookingPage.fillInformation')} </h3>
                    <div className=" grid lg:grid-cols-3 gap-2 lg:gap-5">
                        <div className="mb-4 ">
                            <label className="block  text-gray-700 font-normal">{t('account.name')} </label>
                            <input required
                                   type="text"
                                   value={name}
                                   onChange={(e) => setName(e.target.value)}
                                   className="w-full border-b border-b-gray-300  lg:px-2 py-1 focus:border-blue-500"
                            />
                        </div>
                        <div className="mb-4">
                            <label className="block text-gray-700  font-normal">{t('account.surName')}</label>
                            <input required
                                   type="text"
                                   value={surname}
                                   onChange={(e) => setSurname(e.target.value)}
                                   className="w-full border-b border-b-gray-300 lg:px-2 py-1 focus:border-blue-500"
                            />
                        </div>
                        <div className="mb-4">
                            <label className="block text-gray-700  font-normal">{t('account.phoneNumber')}</label>
                            <input required
                                   type="text"
                                   value={phoneNumber}
                                   onChange={(e) => setPhoneNumber(e.target.value)}
                                   className="w-full border-b border-b-gray-300  lg:px-2 py-1 focus:border-blue-500"
                            />
                        </div>
                    </div>
                    <div className="mb-4">
                        <label className="block text-gray-700 font-semibold">{t('bookingPage.message')}</label>
                        <textarea
                            placeholder={t('bookingPage.message')}
                            value={message}
                            onChange={(e) => setMessage(e.target.value)}
                                rows={4}
                                className="w-full placeholder-gray-700  rounded-2xl px-2 py-1 h-[200px]  border resize-none focus:outline-none focus:border-blue-500"
                            />
                        </div>
                </div>
                <div className="col-span-2 lg:col-span-1 border rounded-2xl shadow-xl p-10 w-full h-max">
                    <h2 className="text-xl font-semibold ">{t('common.total')}</h2>
                    {/* {checkInDate && checkOutDate && ( */}
                        <div className="mt-4 border-t pt-4 mb-4 ">
                            <div className="flex justify-between items-center mb-6">
                                <p className="font-semibold">
                                    {totalNights} {totalNights > 1 ? t('common.days') : t('common.day')} х {room.Price} {t('common.currencySom')}
                                </p>
                                <p className="font-semibold">
                                    {totalCost} {t('common.currencySom')}
                                </p>
                            </div>
                            <div className="flex justify-between">
                                <p className="font-semibold">{t('common.totalAmount')} :</p>
                                <p className="font-semibold">{totalCost} {t('common.currencySom')}</p>
                            </div>
                        </div>
                        <Button
                            style={`${
                                !checkInDate || !checkOutDate 
                                    ? 'bg-button-gray text-gray font-normal text-sm h-12 w-full cursor-default ' 
                                    : 'bg-button-red text-white font-normal text-sm h-12 w-full border-none'
                            }`}
                            name={t('button.book')}
                            onClick={handleBooking}
                            disabled={!checkInDate || !checkOutDate}
                        />
                    {/* )} */}
                </div>
            </div>
        </div>
    );
};

export default ReservationPage;
