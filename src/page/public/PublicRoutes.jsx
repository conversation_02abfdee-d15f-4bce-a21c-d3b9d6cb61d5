import { Route } from "react-router-dom";
import MainPage from "./mainPage/MainPage.jsx";
import AccommodationDetailPage from "./accomodationDetailPage/ui/index.tsx";
import ReservationPage from "./reservationPage/index.tsx";
import UserView from "./userView/index.jsx";
import AboutUs from "./aboutUs/index.jsx";


const PublicRoutes = [
    <Route key="main page" path='/' element={<MainPage />} />,
    <Route key="room" path='/accommodation/:id' element={<AccommodationDetailPage/>} />,
    <Route key="reservation" path='/reservation/:id' element={<ReservationPage/>} />,
    <Route key="user view" path='/userview/:id' element={<UserView/>} />,
    <Route key="aboutUS" path='/about' element={<AboutUs/>} />,
]


export default PublicRoutes;