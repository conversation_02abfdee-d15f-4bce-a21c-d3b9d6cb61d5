import React, { useEffect } from "react";
import { useParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { getUserInfo } from "../../../store/actions/auth/Auth.js";
import UserImage from "../../../shared/assets/user.png";
import { API_IMG } from "shared/config/api.js";
import { Link } from "react-router-dom";
import {useTranslation} from "react-i18next";

function UserView() {
    const {t} = useTranslation()
    const { id } = useParams();
    const dispatch = useDispatch();
    const owner = useSelector(state => state.auth.userInfo[id]);

    useEffect(() => {
        if (id && !owner) {
            dispatch(getUserInfo(id));
        }
    }, [dispatch, id, owner]);

    if (!owner) {
        return <p className="text-center mt-10 text-lg">Загрузка данных владельца...</p>;
    }
    const renderData = (data) => data ? data : 'Нет данных';

    return (
        <div className='container mx-auto sm:px-4 lg:px-8 py-5 flex justify-center'>
            <div className="flex flex-col justify-center items-center w-max">
                <h2 className="text-xl sm:text-2xl  font-bold mb-8 text-blue-text-lock">
                    {t('userView.title')}
                </h2>
                <div className="flex flex-col md:flex-row md:items-center border shadow-xl rounded-2xl p-6 md:p-10 gap-2 md:gap-12 w-full">
                    <div className="flex flex-col items-center">
                        <img
                            className="h-32 w-32 md:h-40 md:w-40 rounded-full mx-auto"
                            src={owner.Avatar ? API_IMG + owner.Avatar : UserImage}
                            alt="photo"
                        />
                        <p className="text-lg sm:text-xl md:text-2xl font-semibold mt-2 text-blue-text-lock">
                            {owner.Nickname}
                        </p>
                    </div>
                    <div className="flex flex-col gap-4">
                        <div className="flex flex-col items-start">
                            <label className="block text-blue-text-lock font-normal  sm:text-base md:text-lg">
                                {t('userView.name')}
                            </label>
                            <p className="text-base sm:text-lg md:text-xl font-semibold text-blue-text-lock">
                                {renderData(owner.Name)}
                            </p>
                        </div>
                        <div className="flex flex-col items-start">
                            <label className="block text-blue-text-lock font-normal  sm:text-base md:text-lg">
                                {t('userView.surname')}
                            </label>
                            <p className="text-base sm:text-lg md:text-xl font-semibold text-blue-text-lock">
                                {renderData(owner.Surname)}
                            </p>
                        </div>
                        <div className="flex flex-col items-start space-y-1">
                            <label className="block text-blue-text-lock font-normal sm:text-base md:text-lg">
                                {t('userView.contactDetails')}
                            </label>
                            <div className='flex'>
                                <img src='/phoneIcon.svg' alt='phone icon' className='w-5 h-5'/>
                                <a href={`tel:${owner.PhoneNumber}`}
                                   className="text-base sm:text-lg md:text-xl font-normal ml-2 text-blue-text-lock">
                                    {owner.PhoneNumber ? `(${owner.PhoneNumber.slice(0, 4)})${owner.PhoneNumber.slice(4).match(/.{1,3}/g).join('-')}` : 'Нет данных'}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default UserView;
