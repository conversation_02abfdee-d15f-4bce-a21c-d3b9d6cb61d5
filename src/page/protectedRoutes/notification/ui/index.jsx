import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
    fetchUserAllNotifications,
    markNotificationAsRead
} from "features/notification/model/notificationAction/index.js";
import NotificationList from "../../../../entities/notification/NotificationList.jsx";
import Pagination from "../../../../shared/ui/pagination/Pagination.jsx";

function NotificationPage() {
    const dispatch = useDispatch();
    const notifications = useSelector(state => state.notifications.items);
    const [page, setPage] = useState(1);
    const [hasMore, setHasMore] = useState(true);
    const pageSize = 10;

    useEffect(() => {
        dispatch(fetchUserAllNotifications({order_by:"-id" }));
    }, [dispatch, page]);

    const handleNotificationClick = (notification) => {
        if (!notification.IsRead) {
            dispatch(markNotificationAsRead(notification.ID));
        }
    };

    const handlePageChange = (newPage) => {
        setPage(newPage);
    };

    return (
        <div className=''>
            <h2 className='text-xl font-semibold'>Уведомления</h2>
            <NotificationList
                notifications={notifications}
                onNotificationClick={handleNotificationClick}
            />
            {/*<Pagination*/}
            {/*    totalPages={totalPages}*/}
            {/*    currentPage={currentPage}*/}
            {/*    onPageChange={onPageChange}*/}
            {/*/>*/}
        </div>
    );
}

export default NotificationPage;
