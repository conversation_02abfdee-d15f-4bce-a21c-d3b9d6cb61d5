import { MdArrowBackIosNew } from "react-icons/md";
import { useState, useEffect } from "react";
import { Route, Routes, useLocation, useNavigate } from "react-router-dom";
import AddPassport from "features/profile/passportInfo/ui/AddPassport.jsx";
import ProfileSettings from "widgets/profile/settings/ui/index.jsx";
import ProfileView from "widgets/profile/account/ui/index.jsx";
import ProfileSidebar from "widgets/profile/profileSidebar/ui/index.jsx";
import MyBookings from "../../../widgets/profile/myBookings/ui/index.jsx";

// TODO:create global hook to get isMobile
function ProfileRouter() {
    const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
    const navigate = useNavigate()
    const location = useLocation();
    const handleResize = () => {
        setIsMobile(window.innerWidth < 768);
    };
    useEffect(() => {
        window.addEventListener('resize', handleResize);
        return () => {
            window.removeEventListener('resize', handleResize);
        };
    }, []);
    const handleBackClick = () => {
        navigate('/profile');
    };
    const isProfilePage = location.pathname === '/profile';

    return (
        <div
            className='container mx-auto mt-2 flex flex-col sm:items-start justify-center sm:flex sm:flex-row md:justify-start sm:w-full h-100'>
            {isMobile ? (!isProfilePage && (
                <div className='w-max flex p-1 justify-center items-center rounded-full mb-3 border cursor-pointer'
                     onClick={handleBackClick}
                >
                    <MdArrowBackIosNew />
                </div>
            )) :
                <ProfileSidebar />
            }
            <Routes>
                {isMobile && <Route path='/' element={<ProfileSidebar />} />}
                <Route path='/personal-info' element={<ProfileView />} />
                <Route path='/settings' element={<ProfileSettings />} />
                <Route path='/add-passport' element={<AddPassport />} />
                <Route path='/my-bookings' element={<MyBookings />} />
            </Routes>
        </div>
    );
}

export default ProfileRouter;
