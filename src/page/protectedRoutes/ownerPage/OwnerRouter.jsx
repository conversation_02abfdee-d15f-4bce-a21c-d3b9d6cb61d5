import { Route, Routes } from "react-router-dom";
import OwnerMainPage from "./ui/OwnerMainPage.jsx";
import Calendar from "widgets/owner/calendar/ui/index.jsx";
import AccommodationPage from "./ui/AccommodationPage.jsx";
import Applications from "../../../widgets/owner/applications/ui/Applications.jsx";
import MyAccommodations from "widgets/owner/myAccommodations/ui/index.tsx";
import EditAccommodationPage from "./ui/EditAccommodationPage.jsx";
import LockManagement from "../../../widgets/owner/lockManagement/index.jsx";

function OwnerRouter() {
    return (
        <div>
            <Routes>
                <Route path='/' element={<OwnerMainPage />}>
                    <Route index element={<MyAccommodations />} />
                    <Route path="my-accommodations" element={<MyAccommodations />} />
                    <Route path="edit-accommodation/:id" element={<EditAccommodationPage />} />
                    <Route path="calendar" element={<Calendar />} />
                    <Route path="accommodation" element={<AccommodationPage />} />
                    <Route path="application" element={<Applications />} />
                    <Route path="lock-management" element={<LockManagement />} />
                </Route>
            </Routes>
        </div>
    );
}

export default OwnerRouter;