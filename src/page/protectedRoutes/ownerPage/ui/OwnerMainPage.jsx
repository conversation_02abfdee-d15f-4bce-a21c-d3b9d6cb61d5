import {NavLink, Outlet, useNavigate} from "react-router-dom";
import { Swiper, SwiperSlide } from "swiper/react";
import 'swiper/swiper-bundle.css';
import {useTranslation} from "react-i18next";
import Button from "../../../../shared/ui/button/Button.jsx";
import {useSelector} from "react-redux";

const links = [
    { to: 'my-accommodations', label: 'ownerPage.yourAdvertisements' },
    { to: 'calendar', label: 'ownerPage.calendar' },
    { to: 'application', label: 'ownerPage.applications' },
    { to: 'lock-management', label: 'ownerPage.lockManagement' },
];

const active = false

function OwnerMainPage() {
    const {t} = useTranslation()
    const user = useSelector(state => state.auth.user?.user);
    const status = useSelector(state => state.auth?.userStatus || "");
    const navigate = useNavigate();

    const handleOk = () => {
        navigate('/')
    }

    console.log(status, 'status')


    return (
        <div className='container mx-auto'>
            {status === 'approved' ? (
                <>
                    <div className='hidden md:flex flex-wrap md:flex-nowrap justify-center gap-10'>
                        {links.map((link, index) => (
                            <NavLink
                                key={index}
                                to={link.to}
                                className={({isActive}) =>
                                    `pb-2 border-b-2 font-semibold ${
                                        isActive ? 'border-b-2 border-b-blue-600' : 'hover:border-b-blue-600 hover:border-b-2 border-b-transparent'
                                    }`
                                }
                            >
                                {t(link.label)}
                            </NavLink>
                        ))}
                    </div>
                    <div className="block md:hidden">
                        <Swiper
                            spaceBetween={12}
                            slidesPerView={2.5}
                            slideToClickedSlide={true}
                        >
                            {links.map((link, index) => (
                                <SwiperSlide key={index} style={{width:'max-content'}}>
                                    <NavLink
                                        to={link.to}
                                        className={({isActive}) =>
                                            `block text-center pt-2 pb-2  border-b-2 font-semibold w-max ${
                                                isActive
                                                    ? 'border-b-2 border-b-blue-600'
                                                    : 'hover:border-b-blue-600 hover:border-b-2 border-b-transparent '
                                            }`
                                        }
                                    >
                                        {t(link.label)}
                                    </NavLink>
                                </SwiperSlide>
                            ))}
                        </Swiper>
                    </div>
                    <div className="mt-5">
                        <Outlet/>
                    </div>
                </>
            ): (
                <div className='flex flex-col items-center justify-center my-12 md:my-0 md:h-[55vh]'>
                    <div className="relative flex items-center justify-center mb-16">
                        <div
                            className="absolute w-[140px] h-[140px] bg-[#********] rounded-full animate-synchronized"></div>
                        <div
                            className="absolute w-[100px] h-[100px] bg-[#********] rounded-full animate-synchronized"></div>
                        <div
                            className="absolute w-[60px] h-[60px] bg-[#********] rounded-full animate-synchronized"></div>

                        <img className="w-[40px] relative z-10" src="/activeProfile.svg" alt="icon"/>
                    </div>
                    <p className='text-center text-blue-text-lock text-xl font-semibold mb-2'>{t('moderation.title')}</p>
                    <p className='text-center mb-4 text-lg'>{t('moderation.message')}</p>
                    <Button style={'px-8 bg-gray-200 hover:bg-gray-400'} name={t('button.close')} onClick={handleOk}/>
                </div>
            )}

        </div>
    );
}

export default OwnerMainPage;
