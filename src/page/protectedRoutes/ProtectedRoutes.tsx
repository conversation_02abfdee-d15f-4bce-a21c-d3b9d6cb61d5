import { Route } from "react-router-dom";
import NotificationPage from "./notification/ui";
import ProfileRouter from "./profilePage/ProfileRouter";
import OwnerRouter from "./ownerPage/OwnerRouter";
import UnauthenticatedRoute from "../../shared/libs/routing/UnauthenticatedRoute";


const ProtectedRoutes = [
    <Route key="profile" path="/profile/*" element={<UnauthenticatedRoute element={<ProfileRouter />} />} />,
    <Route key="notification" path="/notification" element={<UnauthenticatedRoute element={<NotificationPage />} />} />,
    <Route key="owner" path="/owner/*" element={<UnauthenticatedRoute element={<OwnerRouter />} />} />,
]


export default ProtectedRoutes;