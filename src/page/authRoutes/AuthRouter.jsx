import {Route, Routes, useLocation, useNavigate} from "react-router-dom";
import ChangePasswordPage from "./changePassword/ChangePasswordPage";
import ForgotPasswordPage from "./forgotPassword/ForgotPasswordPage";
import ForgotPasswordCodePage from "./forgotPassword/ForgotPasswordCodePage";
import ForgotPasswordEmailPage from "./forgotPassword/ForgotPasswordEmailPage";
import NewPasswordPage from "./forgotPassword/NewPasswordPage";
import Login from "./login/ui/LoginPage";
import Register from "./register/ui/RegisterPage.jsx"
import VerifyEmailPage from "./verifyEmail/VerifyEmailPage";
import {useSelector} from "react-redux";
import {useIsAuthenticated} from "../../store/selectors/AuthSelectors.js";

const AuthRouter = () => {


    return <Routes>
        <Route path='/login' element={<Login />} />
        <Route path='/register' element={<Register/>} />
        <Route path='/forgot-password' element={<ForgotPasswordPage />}>
            <Route index element={<ForgotPasswordEmailPage title="Отправка Email" />} />
            <Route path='email-forgot-password' element={<ForgotPasswordEmailPage title="Отправка Email" />} />
            <Route path='verify-forgot-password' element={<ForgotPasswordCodePage title="Отправка кода" />} />
            <Route path='new-password' element={<NewPasswordPage title="Новый пароль" />} />
        </Route>
        <Route path="/verify-email" element={<VerifyEmailPage />} />
        <Route path="/change-password" element={<ChangePasswordPage />} />
    </Routes>
}

export default AuthRouter;