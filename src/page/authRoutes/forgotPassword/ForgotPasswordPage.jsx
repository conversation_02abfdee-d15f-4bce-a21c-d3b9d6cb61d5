import { Outlet, <PERSON> } from "react-router-dom";
import {useTranslation} from "react-i18next";

function ForgotPasswordPage() {
    const {t} = useTranslation()
    return (
        <div className='bg-white shadow-2xl p-5 md:px-10 rounded-md  m-auto w-[400px] sm:w-[500px]'>
            <h2 className='font-bold text-center  text-xl sm:text-2xl mb-2'>{t('auth.passwordRecovery')}</h2>
            <Outlet />
            <p className='text-center mt-4'>{t('auth.rememberPassword')}
                <Link className='ml-2 font-bold' to='/auth/login'>{t('auth.login')}</Link>
            </p>
        </div>
    );
}

export default ForgotPasswordPage;
