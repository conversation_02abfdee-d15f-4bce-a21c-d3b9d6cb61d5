export const handlePending = (state) => {
    state.status = 'loading';
    state.error = '';
};

export const handleFulfilled = (state, action) => {
    state.status = 'succeeded';
    state.user = action.payload;
    state.isAuthenticated = true;
    localStorage.setItem('userDetails', JSON.stringify(action.payload));
};

export const handleRejected = (state, action) => {
    state.status = 'failed';
    state.error = action.payload;
};
