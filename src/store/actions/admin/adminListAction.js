import {createAsyncThunk} from "@reduxjs/toolkit";
import axiosInstance from "../../../shared/libs/AxiosInstance/index.js";
import {API} from "../../../shared/config/api.js";
import {toast} from "react-toastify";

export const getAllAccomodations = createAsyncThunk(
    'admin/getAllAccomodations',
    async ({page , limit, verification}, { rejectWithValue }) => {
        try {
            const response = await axiosInstance.get(`${API}accommodation/get-all-as-moderator`,{
                params: {limit, page, verification}
            });
            return response.data;

        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);


export const getAllUsers = createAsyncThunk(
    'admin/getAllUsers',
    async ({page , limit, status, delete_request}, { rejectWithValue }) => {
        try {
            const response = await axiosInstance.get(`${API}api/auth/get_all_users`,{
                params: {limit, page, status, delete_request}
            });
            return response.data;

        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);

export const getUserById = createAsyncThunk(
    'admin/getUserById',
    async (id, { rejectWithValue }) => {
        try {
            const response = await axiosInstance.get(`${API}api/auth/get_one/${id}`,{
                params: {id}
            });
            return response.data;

        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);

export const deleteUser = createAsyncThunk(
    'admin/deleteUser',
    async (id, { rejectWithValue }) => {
        try {
            const response = await axiosInstance.delete(`${API}api/auth/delete_user/${id}`);
            toast.success(`Пользовтель ${id} удален`);
            return response.data;

        } catch (error) {
            toast.error("Ошибка при удалении");
            return rejectWithValue(error.response.data);
        }
    }
);


export const activateUser = createAsyncThunk(
    'admin/activateUser',
    async (id, { rejectWithValue }) => {
        try {
            const response = await axiosInstance.post(`${API}api/auth/activate/${id}`);
            return response.data;

        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);

export const deactivateUser = createAsyncThunk(
    'admin/deactivateUser',
    async (id, { rejectWithValue }) => {
        try {
            const response = await axiosInstance.post(`${API}api/auth/deactivate/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);


export const approveUser = createAsyncThunk(
    'admin/approveUser',
    async ({ user_id, status, reason },  { rejectWithValue }) => {

        try {
            const response = await axiosInstance.post(`${API}api/auth/approve_owner?user_id=${user_id}`, {
                status,
                ...(reason && { reason })
            });
            return response.data;
        } catch (error) {
            toast.error('Ошибка при отлонении заявки');
            return rejectWithValue(error.response.data);
        }
    }
);


export const verifyAccommodation = createAsyncThunk(
    'admin/verifyAccommodation',
    async (id, { rejectWithValue }) => {
        try {
            const response = await axiosInstance.patch(`${API}accommodation/verify/${id}`);
            toast.success('Заявка успешно принята!')
            return response.data;
        } catch (error) {
            toast.error('Ошибка при принятии заявки');
            return rejectWithValue(error.response.data);
        }
    }
);



export const unverifyAccommodation = createAsyncThunk(
    'admin/unverifyAccommodation',
    async ({id,rejectionReason},  { rejectWithValue }) => {
        try {
            const response = await axiosInstance.patch(`${API}accommodation/unverify/${id}`, {
                rejectionReason
            });
            toast.success('Заявка отклонена!')
            return response.data;
        } catch (error) {
            toast.error('Ошибка при отлонении заявки');
            return rejectWithValue(error.response.data);
        }
    }
);


export const rejectUserDeletion = createAsyncThunk(
    'admin/rejectUserDeletion',
    async ({id,rejection_reason},  { rejectWithValue }) => {
        try {
            const response = await axiosInstance.post(`${API}api/auth/reject_delete_request/${id}`, {
                rejection_reason
            });
            toast.success('Заявка на удаление отклонена!')
            return response.data;
        } catch (error) {
            toast.error('Ошибка при отлонении заявки');
            return rejectWithValue(error.response.data);
        }
    }
)








