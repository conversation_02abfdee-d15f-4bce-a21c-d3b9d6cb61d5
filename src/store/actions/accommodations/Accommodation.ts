import { createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import { toast } from "react-toastify";
import { API } from "shared/config/api";
import i18next from 'i18next';
import axiosInstance from "shared/libs/AxiosInstance";
import {
    Accommodation,
    Category,
    CreateAccomondationGalleryPayload,
    GetAccommodationsParams
} from "../../../shared/types/sliceTypes";

export const getAccommodations = createAsyncThunk<
    { Count: number; Data: Accommodation[] },
    GetAccommodationsParams,
    { rejectValue: string }
>(
    'accommodations/getAccommodations',
    async (params, { rejectWithValue }) => {
        try {
            // Очистка параметров перед отправкой
            const filteredParams = Object.fromEntries(
                Object.entries(params).filter(([_, value]) => value !== null && value !== undefined && value !== '')
            );

            const serializedParams = new URLSearchParams();
            for (const [key, value] of Object.entries(filteredParams)) {
                if (Array.isArray(value)) {
                    // Если значение — массив, добавляем каждый элемент как отдельный параметр
                    value.forEach(val => serializedParams.append(key, val));
                } else {
                    // Иначе просто добавляем параметр
                    serializedParams.append(key, value);
                }
            }

            const response = await axios.get<{ Count: number; Data: Accommodation[] }>(
                `${API}accommodation/get-all`,
                { params: serializedParams  }
            );

            return response.data;
        } catch (e) {
            toast.error('Ошибка при получении данных');
            return rejectWithValue('Failed to fetch rooms');
        }
    }
);


export const createAccommodation = createAsyncThunk<{
    ID: number | null;
    Count: number;
    Data: Accommodation
}, { data: { Photos: any[] | undefined } }, { rejectValue: string }>(
    "accommodation/createAccommodation",
    async ({ data }, { rejectWithValue, dispatch }) => {
        try {
            const photos = data?.Photos;
            delete data.Photos;

            const response = await axiosInstance.post<{ ID: number | null; Count: number; Data: Accommodation }>(`${API}accommodation/create/`, data);
            const accommodationData = response.data;

            if (accommodationData && accommodationData.ID !== null) {
                const ID = accommodationData.ID;

                if (photos && photos.length > 0) {
                    const formData = new FormData();
                    photos.forEach((image, index) => formData.append('image', image?.file, `${index}.jpg`));

                    await dispatch(createAccomondationGallery({ formData, id: String(ID) }));
                }
            }
            toast.success('Объявление успешно создана!')
            return accommodationData;
        } catch (e) {
            toast.error("Ошибка при создании размещения");
            return rejectWithValue("Failed to create accommodation");
        }
    }
);


export const editAccommodation = createAsyncThunk<{
    ID: number | null;
    Count: number;
    Data: Accommodation
}, { data: { Photos: any[] | undefined }, id: number, imagesToDelete: number[] }, { rejectValue: string }>(
    "accommodation/editAccommodation",
    async ({ data, id, imagesToDelete }, { rejectWithValue, dispatch }) => {
        try {
            // First, delete any images marked for deletion
            if (imagesToDelete && imagesToDelete.length > 0) {
                await dispatch(deleteAccommodationImages({ id, imagesToDelete }));
            }

            const photos = data?.Photos;
            delete data.Photos;

            const response = await axiosInstance.patch<{ ID: number | null; Count: number; Data: Accommodation }>(`${API}accommodation/update/${id}`, data);
            const accommodationData = response.data;

            if (accommodationData && accommodationData.ID !== null) {
                const ID = accommodationData.ID;

                // Upload new photos only
                const newPhotos = photos?.filter(photo => !photo.isExisting) || [];
                if (newPhotos.length > 0) {
                    const formData = new FormData();
                    newPhotos.forEach((image, index) => formData.append('image', image.file, `${index}.jpg`));

                    await dispatch(createAccomondationGallery({ formData, id: String(ID) }));
                }
            }
            toast.success('Изменения добавлены!')
            return accommodationData;
        } catch (e) {
            toast.error("Ошибка при редактировании размещения");
            return rejectWithValue("Failed to edit accommodation");
        }
    }
);

export const createAccomondationGallery = createAsyncThunk<any[], CreateAccomondationGalleryPayload>(
    'accommodation/createAccomondationGallery',
    async ({ formData, id }, { rejectWithValue }) => {
        try {
            const response = <any> await axiosInstance.post(`${API}image/create-accommodation-image/${id}`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });
            return response.data;
        } catch (e) {
            toast.error('Ошибка при добавлении изображений');
            return rejectWithValue('Failed to add images');
        }
    }
);

export const deleteAccommodationImages = createAsyncThunk<
    void,
    { id: number; imagesToDelete: number[] },
    { rejectValue: number }
>(
    "accommodation/deleteAccommodationImages",
    async ({ id, imagesToDelete }, { rejectWithValue }) => {
        try {
            const res = await axiosInstance.delete(`${API}image/delete/${id}`, {
                data: {  // Use the `data` property to send a request body in DELETE
                    Images: imagesToDelete,
                },
            });
        } catch (e) {
            toast.error("Ошибка при удалении изображений");
            return rejectWithValue("Failed to delete images");
        }
    }
);

export const getAccommodationByID = createAsyncThunk(
    'accommodations/getAccommodationByID',
    async (id: number, { rejectWithValue }) => {
        try {
            const response = await axios.get(`${API}accommodation/get-one/${id}`);
            return { id, data: response.data };
        } catch (e) {
            toast.error("Ошибка при получении данных о комнате");
            return rejectWithValue("Failed to fetch room detail");
        }
    }
);

export const getMyAccommodations = createAsyncThunk(
    'accommodations/getMyAccommodations',
    async (_, { rejectWithValue }) => {
        try {
            const response = await axiosInstance.get(`${API}accommodation/my-accommodations?page=${1}&limit=${10**9}`);
            return response.data;
        } catch (e) {
            toast.error("Ошибка при получении данных");
            return rejectWithValue("Failed to fetch room detail");
        }
    }
);

export const removeMyAccommodations = createAsyncThunk(
    'accommodations/removeMyAccommodation',
    async (id: number, { rejectWithValue, dispatch }) => {
        try {
            const response = await axiosInstance.delete(`${API}accommodation/delete/${id}`);
            // dispatch(getMyAccommodations());
            toast.success('Объявление удалено')
            return response.data;
        } catch (e) {
            toast.error("Ошибка при получении данных");
            return rejectWithValue("Failed to fetch room detail");
        }
    }
);

export const activateMyAccommodations = createAsyncThunk(
    'accommodations/activateMyAccommodation',
    async ({ id, updateCallback }: { id: number; updateCallback: () => void }, { rejectWithValue, dispatch }) => {
        try {
            const response = await axiosInstance.post(`${API}accommodation/activate/${id}`);
            if (updateCallback) {
                updateCallback();
            }
            return response.data;
        } catch (e) {
            toast.error("Ошибка при получении данных");
            return rejectWithValue("Failed to fetch room detail");
        }
    }
);

export const deactivateMyAccommodations = createAsyncThunk(
    'accommodations/deactivateMyAccommodation',
    async ({ id, updateCallback }: { id: number; updateCallback: () => void }, { rejectWithValue, dispatch }) => {
        try {
            const response = await axiosInstance.post(`${API}accommodation/deactivate/${id}`);
            if (updateCallback) {
                updateCallback();
            }
            return response.data;
        } catch (e) {
            toast.error("Ошибка при получении данных");
            return rejectWithValue("Failed to fetch room detail");
        }
    }
);

export const AccommodationCategory = createAsyncThunk<Category[], void, { rejectValue: string }>(
    'accommodations/AccommodationCategory',
    async (_, { rejectWithValue }) => {
        try {
            const response = await axios.get<Category[]>(`${API}dictionary/categories`);
            // return response.data;
        } catch (e) {
            toast.error("Ошибка при получении данных");
            return rejectWithValue("Failed to fetch categories");
        }
    }
);

export const Getfacility = createAsyncThunk(
    'accommodations/Getfacility',
    async (_, { rejectWithValue }) => {
        try {
            const response = await axios.get<any[]>(`${API}dictionary/facility/`);
            return response.data;
        } catch (e) {
            toast.error("Ошибка при получении данных");
            return rejectWithValue("Failed to fetch facility");
        }
    }
)




