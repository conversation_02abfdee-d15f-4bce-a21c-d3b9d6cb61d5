import {createAsyncThunk} from "@reduxjs/toolkit";
import axios from "axios";
import {API} from "../../../shared/config/api.js";
import axiosInstance from "../../../shared/libs/AxiosInstance/index.js";
import {toast} from "react-toastify";

export const createNewBooking = createAsyncThunk(
    'booking/createNewBooking',
    async (values, { rejectWithValue }) => {
        try {
            const response = await axiosInstance.post( API+'booking/create', values);
            return response.data
        } catch (e) {
            return rejectWithValue(e.response ? e.response.data : e.message);
        }
    }
);

export const getMyBookings = createAsyncThunk(
    'booking/getMyBookings',
    async ({ status, limit, page }, { rejectWithValue }) => {
        try {
            const response = await axiosInstance.get(`${API}booking/get-my-bookings`, {
                params: { status, limit, page }
            });
            return response.data;

        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);


export const getByAccommodation = createAsyncThunk(
    'booking/getByAccommodation',
    async ({status, page , limit}, { rejectWithValue }) => {
        try {
            const response = await axiosInstance.get(`${API}booking/get-all`,{
                params: {status, limit, page}
            });
            return response.data;

        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);


export const activateBooking = createAsyncThunk(
    'booking/activateBooking',
    async (id, { rejectWithValue }) => {
        try {
            const response = await axiosInstance.post(`${API}booking/activate/${id}`);
            toast.success("Ваша заявка отправлена!")
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);


export const rejectBooking = createAsyncThunk(
    'booking/rejectBooking',
    async ({ id, RejectionReason }, { rejectWithValue }) => {
        try {
            const response = await axiosInstance.post(`${API}booking/reject/${id}`, {RejectionReason});
            toast.success("Вы отклонили заявку!")
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);