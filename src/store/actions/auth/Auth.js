import { createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import { toast } from "react-toastify";
import axiosInstance from "shared/libs/AxiosInstance/index.js";

const API = import.meta.env.VITE_REACT_API_URL

//TODO: error handling convert all error messages to a single function with parameters

export const login = createAsyncThunk(
    'auth/login',
    async (args, { rejectWithValue, dispatch }) => {
        const { email, password, navigate } = args;
        try {
            const response = await axios.post(`${API}api/auth/login`, {
                Email: email,
                Password: password
            });
            navigate('/');
            toast.success('Вы успешно авторизованы!');
            return response.data;
        } catch (error) {
            let err_msg = error?.response?.data?.error.Error;
            if (err_msg) {
                if (err_msg.includes('record not found') || err_msg.includes('crypto/bcrypt: hashedPassword is not the hash of the given password')) {
                    err_msg = 'Неверный логин и пароль!';
                } else if (err_msg.includes('exception: please verify your email')) {
                    err_msg = 'Вы не активировали свою почту';
                }
            }
            toast.error(err_msg);
            return rejectWithValue(err_msg);
        }
    }
);


export const googleLogin = createAsyncThunk(
    'auth/googleLogin',
    async (args, { rejectWithValue }) => {
        const { token, navigate } = args;
        try {
            const response = await axios.get(`${API}google-auth/exchange-token?token=${token}`);
            toast.success('Google аккаунт успешно авторизован!')
            navigate('/');
            return response.data;
        } catch (error) {
            let err_msg = error?.response?.data?.error.Error;
            if (err_msg && err_msg.includes('error')) {
                err_msg = 'ошибка!';
            }
            toast.error(err_msg);
            return rejectWithValue(err_msg);
        }
    }
);

export const register = createAsyncThunk(
    'auth/register',
    async (args, { rejectWithValue }) => {
        const { email, password, passwordConfirm, nickname, navigate } = args;
        try {
            const response = await axios.post(API + 'api/auth/registration', {
                Email: email,
                Password: password,
                PasswordConfirm: passwordConfirm,
                Nickname: nickname
            })
            navigate('/auth/verify-email');
            return response.data
        } catch (error) {
            let err_msg = error?.response?.data?.error.Error;
            if (err_msg && err_msg.includes('exception:email already exists')) {
                err_msg = 'Пользователь с такой почтой уже существует!';
            }
            toast.error(err_msg);
            return rejectWithValue(err_msg);
        }
    }
)

export const changePassword = createAsyncThunk(
    'auth/changePassword',
    async (args, { rejectWithValue }) => {
        const { values, navigate } = args;
        try {
            const response = await axiosInstance.post('api/auth/change_password', values);
            toast.success('Пароль успешно обновлен!')
            navigate('/')
            return response.data;
        } catch (error) {
            let err_msg = error?.response?.data?.error.Error;
            if (err_msg && (err_msg.includes('record not found') || err_msg.includes('crypto/bcrypt: hashedPassword is not the hash of the given password'))) {
                err_msg = 'Старый пароль неверный!';
            }
            toast.error(err_msg);
            return rejectWithValue(err_msg);
        }
    }
);

export const sendForgotPasswordEmail = createAsyncThunk(
    'auth/sendForgotPasswordEmail',
    async (email, { rejectWithValue }) => {
        try {
            const response = await axios.post(API + 'api/auth/forgot_password', { email });
            return response.data
        } catch (error) {
            let err_msg = error?.response?.data?.error.Error;
            if (err_msg && err_msg.includes('record not found')) {
                err_msg = 'Электронная почта не найдена!';
            }
            toast.error(err_msg);
            return rejectWithValue(err_msg);
        }
    }
);

export const sendForgotPasswordCode = createAsyncThunk(
    'auth/sendForgotPasswordCode',
    async (code, { rejectWithValue }) => {
        try {
            const response = await axios.get(`${API}api/auth/verify_forgot_password/${code}`);
            response.data
        } catch (error) {
            let err_msg = error?.response?.data?.error.Error;
            if (err_msg && err_msg.includes('redis: nil')) {
                err_msg = 'Неверный код подтверждения!';
            }
            toast.error(err_msg);
            return rejectWithValue(err_msg);
        }
    }
);

export const createNewPassword = createAsyncThunk(
    'auth/createNewPassword',
    async (values, { rejectWithValue }) => {
        try {
            const response = await axios.post(API + 'api/auth/create_new_password', values);
            toast.success('Пароль успешно создан!')
            return response.data
        } catch (e) {
            return rejectWithValue(e.response ? e.response.data : e.message);
        }
    }
);

export const verifyEmail = createAsyncThunk(
    'auth/verifyEmail',
    async (args, { rejectWithValue }) => {
        const { values, navigate } = args;
        try {
            const response = await axios.post(`${API}api/auth/verify_email/${values}`);
            navigate('/auth/login');
            toast.success('Аккаунт успешно создан')
            return response.data;
        } catch (error) {
            let err_msg = error?.response?.data?.error.Error;
            if (err_msg && err_msg.includes('exception:email already exists')) {
                err_msg = 'Пользователь с такой почтой уже существует!';
            }
            toast.error(err_msg);
            return rejectWithValue(err_msg);
        }
    }
);

export const resendVerificationCode = createAsyncThunk(
    'auth/resendVerificationCode',
    async (email, { rejectWithValue }) => {
        try {
            const response = await axios.post(`${API}api/auth/resend_verification_code`, { email });
            toast.success('Код отправлен на вашу почту.');
            return response.data;
        } catch (error) {
            let err_msg = error?.response?.data?.error.Error;
            if (err_msg && err_msg.includes('record not found')) {
                err_msg = 'Почта не найдена!';
            }
            toast.error(err_msg);
            return rejectWithValue(err_msg);
        }
    }
);

export const logout = createAsyncThunk(
    'auth/logout',
    async (token, { rejectWithValue, dispatch }) => {
        try {
            const response = await axiosInstance.post(`${API}api/auth/logout`, { token });
            toast.success('Вы успешно вышли');
            return response.data;
        } catch (error) {
            const status = error?.response?.status;
            let err_msg = error?.response?.data?.error?.Error || 'Ошибка выхода';

            // Если токен просрочен или уже удалён — принудительно чистим клиент
            if (status === 401 || status === 403) {
                console.warn('Токен невалиден, делаем принудительный логаут');

                // Очищаем localStorage и вызываем logout reducer напрямую
                localStorage.removeItem('userDetails');
                dispatch({ type: 'auth/logout/fulfilled' });

                toast.info('Сессия истекла. Вы вышли из аккаунта.');
                return; // не возвращаем reject, чтобы thunk не падал
            }

            toast.error(err_msg);
            return rejectWithValue(err_msg);
        }
    }
);




export const getUserInfo = createAsyncThunk(
    'auth/getUserInfo',
    async (id, { rejectWithValue }) => {
        try {
            const response = await axios.get(`${API}api/auth/get_user_info/${id}`);
            return { id, data: response.data };
        } catch (error) {
            let err_msg = error?.response?.data?.error.Error;
            if (err_msg && err_msg.includes('error')) {
                err_msg = 'ошибка!';
            }
            return rejectWithValue(err_msg);
        }
    }
);
export const fetchProfile = createAsyncThunk(
    'auth/fetchProfile',
    async (_, { getState, rejectWithValue }) => {
        const token = getState().auth.user.token || localStorage.getItem('token');
        try {
            const response = await axiosInstance.get(`${API}api/auth/profile`, {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });

            const { Name, Surname, PhoneNumber, Role, status } = response.data.Profile;
            return { Name, Surname, PhoneNumber, Role, status };
        } catch (error) {
            const err_msg = handleError(error);
            return rejectWithValue(err_msg);
        }
    }
);


export const deleteMyAccount = createAsyncThunk(
    'auth/deleteMyAccount',
    async (token, { rejectWithValue }) => {
        try {
            const response = await axiosInstance.delete(`${API}api/auth/delete_me`, { token });
            return response.data;
        } catch (error) {
            let err_msg = error?.response?.data?.error.Error;
            if (err_msg && err_msg.includes('delete Error')) {
                err_msg = 'delete Error!';
            }
            toast.error(err_msg);
            return rejectWithValue(err_msg);
        }
    }
);




