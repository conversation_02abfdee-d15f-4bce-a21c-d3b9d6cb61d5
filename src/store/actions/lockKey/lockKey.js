import { createAsyncThunk } from "@reduxjs/toolkit";
import axiosInstance from "../../../shared/libs/AxiosInstance/index.js";
import { API } from "../../../shared/config/api.js";
import {toast} from "react-toastify";

export const getLocks = createAsyncThunk(
    'lockKey/getLocks',
    async (id, { rejectWithValue }) => {
        try {
            const response = await axiosInstance.get(`${API}lock/get-by-accommodation/${id}`);
            return response.data;
        } catch (e) {
            return rejectWithValue(e.response ? e.response.data : e.message);
        }
    }
);

export const createNewLockKey = createAsyncThunk(
    'lockKey/createNewKey',
    async (values, { rejectWithValue }) => {
        try {
            const response = await axiosInstance.post(`${API}passcode/`, values);
            toast.success('Ключ успешно создан!')
            return response.data;
        } catch (e) {
            return rejectWithValue(e.response ? e.response.data : e.message);
        }
    }
);

export const updateLockKey = createAsyncThunk(
    'lockKey/updateKey',
    async ({ id, values }, { rejectWithValue }) => {
        try {
            const response = await axiosInstance.patch(`${API}passcode/${id}`, values);
            toast.success('Ключ обновлен!')
            return response.data;
        } catch (e) {
            return rejectWithValue(e.response ? e.response.data : e.message);
        }
    }
);

export const deleteLockKey = createAsyncThunk(
    'lockKey/deleteKey',
    async (id, { rejectWithValue }) => {
        try {
            const response = await axiosInstance.delete(`${API}passcode/${id}`);
            toast.success('Ключ удален!')
            return response.data;
        } catch (e) {
            return rejectWithValue(e.response ? e.response.data : e.message);
        }
    }
);



export const openLock = createAsyncThunk(
    'lockKey/openLock',
    async (id, { rejectWithValue }) => {
        try {
            const response = await axiosInstance.post(`${API}lock/unlock/${id}`);
            toast.success('Замок открыть!')
            return response.data;
        } catch (e) {
            toast.error('Ошибка подключения, проверьте соединение замка с интернетом')
            return rejectWithValue(e.response ? e.response.data : e.message);
        }
    }
);

export const closedLock = createAsyncThunk(
    'lockKey/closedLock',
    async (id, { rejectWithValue }) => {
        try {
            const response = await axiosInstance.post(`${API}lock/lock/${id}`);
            toast.success('Замок закрыт!')
            return response.data;
        } catch (e) {
            toast.error('Ошибка подключения, проверьте соединение замка с интернетом')
            return rejectWithValue(e.response ? e.response.data : e.message);
        }
    }
);


export const getTTLocks = createAsyncThunk(
    'lockKey/getTTLocks',
    async (id, { rejectWithValue }) => {
        try {
            const response = await axiosInstance.get(`${API}lock/get-all-ttlock`);
            return response.data;
        } catch (e) {
            return rejectWithValue(e.response ? e.response.data : e.message);
        }
    }
);


export const createTTLocks = createAsyncThunk(
    'lockKey/createTTLocks',
    async ({ Username, AccommodationId, LockName, Date }, { rejectWithValue }) => {
        try {
            const payload = {
                Username,
                AccommodationId,
                LockName,
                Date,
            };
            const response = await axiosInstance.post(`${API}lock/create`, payload);
            toast.success('Замок успешно привязан!')
            return response.data;
        } catch (e) {
            toast.error('Ошибка подключения привязки замка!')
            return rejectWithValue(e.response ? e.response.data : e.message);
        }
    }
);


export const deleteTTLock = createAsyncThunk(
    'lockKey/deleteTTlock',
    async ( id , { rejectWithValue }) => {
        try {
            const response = await axiosInstance.delete(`${API}lock/delete/${id}`);
            toast.success('Замок успешно удален от жилья!')
            return response.data;
        } catch (e) {
            toast.error('Ошибка при удалении замка от жилья!')
            return rejectWithValue(e.response ? e.response.data : e.message);
        }
    }
);




