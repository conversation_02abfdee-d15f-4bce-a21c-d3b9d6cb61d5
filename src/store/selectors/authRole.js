import { createSelector } from '@reduxjs/toolkit';

export const selectUserRole = (state) => state.auth.role;

export const selectIsModerator = createSelector(
    selectUserRole,
    (role) => role === 'moderator'
);

export const selectIsOwner = createSelector(
    selectUserRole,
    (role) => role === 'owner'
);

export const selectIsUser = createSelector(
    selectUserRole,
    (role) => role === 'user'
);
