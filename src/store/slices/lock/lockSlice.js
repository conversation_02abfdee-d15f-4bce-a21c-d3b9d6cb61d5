import {createSlice} from "@reduxjs/toolkit";
import createGenericBuilder from "../../../shared/libs/BuilderGeneric/BuilderGeneric.js";
import {getByAccommodation, getMyBookings} from "../../actions/booking/Booking.js";
import {
    closedLock,
    createNewLockKey, createTTLocks,
    deleteLockKey,
    deleteTTLock,
    getLocks,
    getTTLocks,
    openLock,
    updateLockKey
} from "../../actions/lockKey/lockKey.js";



const initialState = {
    locks: [],
    ttlocks:[],
    keys: [],
    status: '',
    error: null,
    loading: null,
    loadingOpenLocks: {},
    loadingCloseLocks: {},
}

const lockKeySlice = createSlice({
    name: 'lockKey',
    initialState,
    reducers: {},
    extraReducers: (builder) => {
        const genericBuilder = createGenericBuilder(builder,
            getLocks,
            createNewLockKey,
            updateLockKey,
            deleteTTLock,
            getTTLocks,
            createTTLocks,
        );

        genericBuilder
            .fulfilled(getLocks, (state, action) => {
                state.locks = action.payload;
            })
            .pending(createNewLockKey, (state, action) => {
                state.loading = true;
            })
            .fulfilled(createNewLockKey, (state, action) => {
                state.keys.push(action.payload);
                state.loading = false;
            })
            .rejected(createNewLockKey,(state, action) => {
                state.loading = false;
            })
            .pending(updateLockKey, (state, action) => {
                state.loading = true;
            })
            .fulfilled(updateLockKey, (state, action) => {
                const index = state.keys.findIndex(key => key.ID === action.payload.ID);
                if (index !== -1) {
                    state.keys[index] = action.payload;
                }
                state.loading = false;
            })
            .pending(deleteLockKey, (state, action) => {
                state.loading = true;
            })
            .fulfilled(deleteLockKey, (state, action) => {
                state.keys = state.keys.filter(key => key.ID !== action.meta.arg);
                state.loading = false;
            })
            .pending(openLock, (state, action) => {
                state.loadingOpenLocks[action.meta.arg] = true; // Устанавливаем загрузку для открытия замка
            })
            .fulfilled(openLock, (state, action) => {
                state.loadingOpenLocks[action.meta.arg] = false; // Отключаем загрузку для открытия замка
            })
            .rejected(openLock, (state, action) => {
                state.loadingOpenLocks[action.meta.arg] = false; // Отключаем загрузку в случае ошибки
            })
            .pending(closedLock, (state, action) => {
                state.loadingCloseLocks[action.meta.arg] = true; // Устанавливаем загрузку для закрытия замка
            })
            .fulfilled(closedLock, (state, action) => {
                state.loadingCloseLocks[action.meta.arg] = false; // Отключаем загрузку для закрытия замка
            })
            .rejected(closedLock, (state, action) => {
                state.loadingCloseLocks[action.meta.arg] = false; // Отключаем загрузку в случае ошибки
            })
            .fulfilled(getTTLocks, (state, action) => {
                state.ttlocks = action.payload.List;
            })
            .pending(createTTLocks, (state) => {
                state.loading = true;
                state.error = null; // Очищаем предыдущую ошибку
            })
            .fulfilled(createTTLocks, (state, action) => {
                state.loading = false;
            })
            .rejected(createTTLocks, (state, action) => {
                state.loading = false;
            })
            .pending(deleteTTLock, (state, action) => {
                state.loading = true;
            })
            .fulfilled(deleteTTLock, (state, action) => {
                state.ttlocks = state.ttlocks.filter(key => key.ID !== action.meta.arg);
                state.loading = false;
            })

        genericBuilder.includeCases();
    }
})

export default lockKeySlice.reducer;
