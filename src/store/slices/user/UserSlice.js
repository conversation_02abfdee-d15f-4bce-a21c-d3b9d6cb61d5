// import { createSlice } from "@reduxjs/toolkit";
//
// import { handlePending, handleFulfilled, handleRejected } from '../../stateHelpers.js';
//
// import {fetchUserProfile} from "../../actions/user/User.js";
//
//
// const initialState = {
//     user: JSON.parse(localStorage.getItem('userProfile')) || null,
//     status: 'idle',
//     error: ''
// };
//
// const userSlice = createSlice({
//     name: 'user',
//     initialState,
//     reducers: {},
//     extraReducers: (builder) => {
//         builder
//             .addCase(fetchUserProfile.pending, handlePending)
//             .addCase(fetchUserProfile.fulfilled, (state, action) => {
//                 state.status = 'succeeded';
//                 state.user = action.payload;
//                 localStorage.setItem('userProfile', JSON.stringify(action.payload));
//             })
//             .addCase(fetchUserProfile.rejected, handleRejected);
//     }
// });
//
// export default userSlice.reducer;
