import {createSlice} from "@reduxjs/toolkit";
import createGenericBuilder from "../../../shared/libs/BuilderGeneric/BuilderGeneric.js";
import {getAllAccomodations, getAllUsers, getUserById} from "../../actions/admin/adminListAction.js";
import {getAccommodationByID} from "../../actions/accommodations/Accommodation.ts";



const initialState = {
    users: [],
    userById: [],
    bookings: [],
    accommodations: [],
    accommodationById: [],
    status: '',
    error: null,
    loading: null,
}

const adminListSlice = createSlice({
    name: 'admin',
    initialState,
    reducers: {
        updateUsers: (state, action) => {
            state.users = action.payload;
        },
    },
    extraReducers: (builder) => {
        const genericBuilder = createGenericBuilder(builder,
            getAllAccomodations,
            getAllUsers
        );
        genericBuilder
            .fulfilled(getAllAccomodations, (state, action) => {
                state.accommodations = action.payload;
            })
            .fulfilled(getAccommodationByID, (state, action) => {
                state.accommodationById = action.payload.data.Accommodation;
            })
            .fulfilled(getAllUsers, (state, action) => {
                state.users = action.payload;
            })
            .fulfilled(getUserById, (state, action) => {
                state.userById = action.payload;
            })

        genericBuilder.includeCases();
    }
})

export const { updateUsers } = adminListSlice.actions;
export default adminListSlice.reducer