import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axiosInstance from "../../../shared/libs/AxiosInstance";
import {toast} from "react-toastify";

const BASE_URL = "/dictionary/";

// Функция для создания асинхронных действий (CRUD)
const fetchEntity = (entity) => createAsyncThunk(
    `admin/${entity}/get-all`,
    async ({page, limit}) => {
        const response = await axiosInstance.get(`${BASE_URL}${entity}/get_all`, {
            params: { page, limit },
            headers: {'Accept-Language': '' }
        });


        return response.data;
    }
);

const createEntity = (entity) => createAsyncThunk(
    `admin/${entity}/create`,
    async (newData) => {
        const response = await axiosInstance.post(`${BASE_URL}${entity}/`, newData);
        toast.success("Категория успешно создана!");
        return response.data;
    }
);

const updateEntity = (entity) => createAsyncThunk(
    `admin/${entity}/update`,
    async ({ id, updatedData }) => {
        const response = await axiosInstance.put(`${BASE_URL}${entity}/${id}`, updatedData);
        toast.success("Категория успешно обновлена!");
        return response.data;
    }
);

const deleteEntity = (entity) => createAsyncThunk(
    `admin/${entity}/delete`,
    async (id) => {
        await axiosInstance.delete(`${BASE_URL}${entity}/${id}`);
        toast.warning("Категория успешно удалена!");
        return id;
    }
);

// Список сущностей
const entities = ["categories", "rules", "facilities"];

const initialState = entities.reduce((acc, entity) => {
    acc[entity] = { data: [], status: "idle", error: null };
    return acc;
}, {});

const adminEntitySlice = createSlice({
    name: "adminEntity",
    initialState,
    reducers: {},
    extraReducers: (builder) => {
        entities.forEach((entity) => {
            builder
                .addCase(fetchEntity(entity).pending, (state) => {
                    state[entity].status = "loading";
                })
                .addCase(fetchEntity(entity).fulfilled, (state, action) => {
                    state[entity].status = "succeeded";
                    state[entity].data = action.payload;
                })
                .addCase(fetchEntity(entity).rejected, (state, action) => {
                    state[entity].status = "failed";
                    state[entity].error = action.error.message;
                })

                .addCase(createEntity(entity).fulfilled, (state, action) => {
                    state[entity].data.push(action.payload);
                })

                .addCase(updateEntity(entity).fulfilled, (state, action) => {
                    const index = state[entity].data.findIndex((item) => item.id === action.payload.id);
                    if (index !== -1) {
                        state[entity].data[index] = action.payload;
                    }
                })

                .addCase(deleteEntity(entity).fulfilled, (state, action) => {
                    state[entity].data = state[entity].data.filter((item) => item.id !== action.payload);
                });
        });
    },
});

export const adminActions = {
    fetchCategories: fetchEntity("categories"),
    fetchRules: fetchEntity("rules"),
    fetchFacilities: fetchEntity("facilities"),
    createCategory: createEntity("categories/add_category"),
    createRule: createEntity("rules/add_rule"),
    createFacility: createEntity("facilities/add_facility"),
    updateCategory: updateEntity("categories/update"),
    updateRule: updateEntity("rules/update_rule"),
    updateFacility: updateEntity("facilities/update_facility"),
    deleteCategory: deleteEntity("categories/delete"),
    deleteRule: deleteEntity("rules/delete_rule"),
    deleteFacility: deleteEntity("facilities/delete_facility"),
};

export default adminEntitySlice.reducer;
