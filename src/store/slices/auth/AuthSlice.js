import { createSlice } from "@reduxjs/toolkit";
import createGenericBuilder from "shared/libs/BuilderGeneric/BuilderGeneric.js";
import {
    login,
    googleLogin,
    register,
    fetchProfile,
    verifyEmail,
    resendVerificationCode,
    sendForgotPasswordEmail,
    sendForgotPasswordCode,
    createNewPassword,
    logout, getUserInfo, deleteMyAccount
} from 'store/actions/auth/Auth.js';
import {
    updateOwner,
    updateUserAvatar,
    updateUserInfo,
    updateUserPassportImages
} from "../../../features/profile/model/ProfileAction.js";

const savedUserDetails = JSON.parse(localStorage.getItem('userDetails')) || null;
const userRole = savedUserDetails && savedUserDetails.user ? savedUserDetails.user.Role : null;
const userStatus = savedUserDetails && savedUserDetails.user ? savedUserDetails.user.status : null;

const initialState = {
    user: savedUserDetails,
    isAuthenticated: !!savedUserDetails,
    status: 'idle',
    userInfo: {},
    userInfoAll: [],
    userStatus: userStatus,
    profile: [],
    role: userRole,
    error: ''
};

const authSlice = createSlice({
    name: 'auth',
    initialState,
    reducers: {
        verifyCode(state, action) {
            state.email = action.payload;
        },
    },
    extraReducers: (builder) => {
        const genericBuilder = createGenericBuilder(builder,
            login,
            googleLogin,
            register,
            verifyEmail,
            resendVerificationCode,
            sendForgotPasswordEmail,
            sendForgotPasswordCode,
            createNewPassword,
            logout,
            updateUserAvatar,
            updateUserPassportImages,
            updateUserInfo,
            updateOwner,
            deleteMyAccount
        );
        genericBuilder
            .fulfilled(login, (state, action) => {
                state.user = action.payload;
                state.isAuthenticated = true;
                state.role = action.payload.user.Role;
                localStorage.setItem('userDetails', JSON.stringify(action.payload));

            })
            .fulfilled(googleLogin, (state, action) => {
                state.user = action.payload;
                state.isAuthenticated = true;
                localStorage.setItem('userDetails', JSON.stringify(action.payload));
            })
            .fulfilled(verifyEmail, (state, action) => {
                state.user = action.payload;
            })
            .fulfilled(updateUserAvatar, (state, action) => {
                const currentUser = JSON.parse(localStorage.getItem('userDetails'));
                const updatedUser = { ...currentUser, user: action.payload };
                state.user = updatedUser;
                localStorage.setItem('userDetails', JSON.stringify(updatedUser));
            })
            .fulfilled(updateUserInfo, (state, action) => {
                const currentUser = JSON.parse(localStorage.getItem('userDetails'));
                const updatedUser = { ...currentUser, user: action.payload };
                state.user = updatedUser;
                localStorage.setItem('userDetails', JSON.stringify(updatedUser));
            })
            .fulfilled(updateUserPassportImages, (state, action) => {
                const currentUser = JSON.parse(localStorage.getItem('userDetails'));
                const updatedUser = { ...currentUser, user: action.payload };
                state.user = updatedUser;
                localStorage.setItem('userDetails', JSON.stringify(updatedUser));
            })
            .fulfilled(updateOwner, (state, action) => {
                const currentUser = JSON.parse(localStorage.getItem('userDetails'));
                const updatedUser = { ...currentUser, user: action.payload };
                state.user = updatedUser;
                localStorage.setItem('userDetails', JSON.stringify(updatedUser));
            })
            .fulfilled(logout, (state, action) => {
                state.user = null;
                state.isAuthenticated = false;
                state.role = null;
                localStorage.removeItem('userDetails');
            })
            .fulfilled(getUserInfo, (state, action) => {
                state.status = 'succeeded';
                state.userInfo[action.payload.id] = action.payload.data;
                if (!state.userInfoAll.includes(action.payload.id)) {
                    state.userInfoAll.push(action.payload.id);
                }
            })
            .fulfilled(fetchProfile, (state, action) => {
                const currentUser = JSON.parse(localStorage.getItem('userDetails')) || {};
                const updatedUser = {
                    ...currentUser,
                    user: {
                        ...currentUser.user,
                        ...action.payload,
                    },
                };

                state.user = updatedUser;
                state.userStatus = action.payload.status;
                state.role = action.payload.Role;

                localStorage.setItem('userDetails', JSON.stringify(updatedUser));
            })



        genericBuilder.includeCases();


    }
});

export const { verifyCode } = authSlice.actions;

export default authSlice.reducer;





