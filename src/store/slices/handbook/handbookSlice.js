import { createSlice } from '@reduxjs/toolkit';
import { axiosBaseQuery } from 'shared/libs/AxiosInstance';
import { createApi } from '@reduxjs/toolkit/query/react';
import i18next from "i18next";

const initialState = {
  collateralTypes: {
    isLoading: false,
    data: null,
    error: null,
    single: null
  }
};

export const handbookApi = createApi({
  baseQuery: axiosBaseQuery({
    prepareHeaders: (headers) => {
      headers['Accept-Language'] = i18next.language || 'en';
      return headers;
    }
  }),
  endpoints: (builder) => ({
    getHandbook: builder.query({
      query: (name) => `/${name}`,
      providesTags: ['Handbook'],
    })
  })
});



export const { useGetHandbookQuery } = handbookApi;

const handbookSlice = createSlice({
  name: 'handbook',
  initialState,
  reducers: {}
});
export default handbookSlice.reducer;

