import {createSlice} from "@reduxjs/toolkit";
import createGenericBuilder from "../../../shared/libs/BuilderGeneric/BuilderGeneric.js";
import {getByAccommodation, getMyBookings} from "../../actions/booking/Booking.js";



const initialState = {
    bookings: [],
    accommodationsBooking: [],
    status: '',
    error: null,
    loading: null,
}

const bookingSlice = createSlice({
    name: 'booking',
    initialState,
    reducers: {},
    extraReducers: (builder) => {
        const genericBuilder = createGenericBuilder(builder,
            getMyBookings
        );
        genericBuilder
            .fulfilled(getMyBookings, (state, action) => {
                state.bookings = action.payload.Data;
            })
            .fulfilled(getByAccommodation, (state, action) => {
                state.accommodationsBooking = action.payload.Data;
            })
        genericBuilder.includeCases();
    }
})

export default bookingSlice.reducer