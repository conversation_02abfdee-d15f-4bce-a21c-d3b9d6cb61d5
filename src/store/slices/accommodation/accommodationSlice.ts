import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import {
    AccommodationCategory,
    createAccommodation,
    getAccommodationByID,
    getAccommodations,
    Getfacility,
    getMyAccommodations
} from "../../actions/accommodations/Accommodation";
import { Accommodation, AccommodationState, Category, FacilityCategory } from "../../../page/public/mainPage/interface";

const initialState: AccommodationState = {
    accommodations: [],
    category: [],
    facility: [],
    searchTerm: '',
    accommodationByID: {},
    accommodationAllByID: [],
    myAccommodations: [],
    status: 'idle',
    count: 0,
    error: null,
    hasMore: true,
    id: null
};

const accommodationSlice = createSlice({
    name: 'accommodation',
    initialState,
    reducers: {
        setSearchTerm: (state, action) => {
            state.searchTerm = action.payload;
        },
        clearAccommodations: (state) => {
            state.accommodations = [];
            state.hasMore = true;
            state.count = 0
        },
    },
    extraReducers: (builder) => {
        builder
                .addCase(getAccommodations.pending, (state) => {
                    state.status = 'loading';
                    state.error = null;
                })
                .addCase(getAccommodations.fulfilled, (state, action: PayloadAction<{ Count: number; Data: Accommodation[] }>) => {
                        state.status = 'succeeded';
                    if (action.payload.Data.length < 15) {
                        state.hasMore = false;
                    }

                    const newAccommodations = action.payload.Data.filter(
                        (newItem) => !state.accommodations.some((existingItem) => existingItem.ID === newItem.ID)
                    );

                    state.accommodations = [...state.accommodations, ...newAccommodations];
                    state.count = action.payload.Count;
                })
                .addCase(getAccommodations.rejected, (state, action) => {
                state.status = 'failed';
                state.error = action.error?.message || 'Failed to fetch accommodations';
            })
            .addCase(getAccommodationByID.pending, (state) => {
                state.status = 'loading';
                state.error = null;
            })
            .addCase(getAccommodationByID.fulfilled, (state, action: PayloadAction<{ id: number; data: Accommodation }>) => {
                state.status = 'succeeded';
                state.accommodationByID[action.payload.id] = action.payload.data;
                if (!state.accommodationAllByID.includes(action.payload.id)) {
                    state.accommodationAllByID.push(action.payload.id);
                }
            })
            .addCase(getAccommodationByID.rejected, (state, action) => {
                state.status = 'failed';
                state.error = action.error?.message || 'Failed to fetch accommodation by ID';
            })
            .addCase(AccommodationCategory.pending, (state) => {
                state.status = 'loading';
                state.error = null;
            })
            .addCase(AccommodationCategory.fulfilled, (state, action: PayloadAction<Category[]>) => {
                state.status = 'succeeded';
                state.category = action.payload;
            })
            .addCase(AccommodationCategory.rejected, (state, action) => {
                state.status = 'failed';
                state.error = action.error?.message || 'Failed to fetch categories';
            })
            .addCase(Getfacility.pending, (state) => {
                state.status = 'loading';
                state.error = null;
            })
            .addCase(Getfacility.fulfilled, (state, action: PayloadAction<FacilityCategory[]>) => {
                state.status = 'succeeded';
                state.facility = action.payload;
            })
            .addCase(Getfacility.rejected, (state, action) => {
                state.status = 'failed';
                state.error = action.error?.message || 'Failed to fetch facilities';
            })
            .addCase(getMyAccommodations.pending, (state) => {
                state.status = 'loading';
                state.error = null;
            })
            .addCase(getMyAccommodations.fulfilled, (state, action) => {
                state.status = 'succeeded';
                state.myAccommodations = action.payload;
            })
            .addCase(getMyAccommodations.rejected, (state, action) => {
                state.status = 'failed';
                state.error = action.error?.message || 'Failed to fetch my accommodations';
            });
    }
});

export const { setSearchTerm, clearAccommodations } = accommodationSlice.actions;

export default accommodationSlice.reducer;
