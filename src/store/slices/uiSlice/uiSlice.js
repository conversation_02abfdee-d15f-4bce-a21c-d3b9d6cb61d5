import { createSlice } from '@reduxjs/toolkit';

const uiSlice = createSlice({
    name: 'ui',
    initialState: {
        isMapVisible: false,
    },
    reducers: {
        toggleMap: (state) => {
            state.isMapVisible = !state.isMapVisible;
        },
        setMapVisible: (state, action) => {
            state.isMapVisible = action.payload;
        },
    },
});

export const { toggleMap, setMapVisible } = uiSlice.actions;
export default uiSlice.reducer;
