// components/MapView.jsx
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMapEvents } from 'react-leaflet';
import L from 'leaflet';
import { useEffect, useState } from 'react';

const MapView = ({ data, onBoundsChange }) => {
    const [mapRef, setMapRef] = useState(null);

    const handleMoveEnd = () => {
        if (mapRef) {
            const bounds = mapRef.getBounds();
            onBoundsChange(bounds);
        }
    };

    return (
        <MapContainer
            center={[42.8748, 74.5932]}
            zoom={13}
            scrollWheelZoom={true}
            whenCreated={setMapRef}
            style={{ height: '600px', width: '100%' }}
            onmoveend={handleMoveEnd}
            onzoomend={handleMoveEnd}
        >
            <TileLayer
                url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
            />
            {data.map((item) => (
                <Marker key={item.ID} position={[+item.Latitude, +item.Longitude]}>
                    <Popup>{item.Title}</Popup>
                </Marker>
            ))}
        </MapContainer>
    );
};

export default MapView;
