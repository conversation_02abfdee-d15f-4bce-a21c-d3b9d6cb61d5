import { useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { MdOutlinePhotoCamera } from 'react-icons/md';
import UserImage from 'shared/assets/user.png';
import {API_IMG} from "../../../../../shared/config/api.js";

const AvatarSection = ({ setAvatarPreview, setIsModalOpen, avatarInputRef  }) => {
    const user = useSelector(state => state.auth.user.user);

    const handleFileChange = (e) => {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onloadend = () => {
                setAvatarPreview(reader.result);
                setIsModalOpen(true);
            };
            reader.readAsDataURL(file);
        }
    };

    const handleImageError = (e) => {
        e.target.src = UserImage;
    };

    return (
        <div className='relative'>
            <img className='rounded-full w-[150px] h-[150px] md:w-40 md:h-40 object-cover'
                 src={user?.Profile?.Avatar ? API_IMG+user?.Profile?.Avatar : UserImage}
                 alt=''
                 onError={handleImageError}
            />
            <span className='absolute right-0 z-10 bottom-0 bg-white rounded-full p-1 cursor-pointer'
                  onClick={() => avatarInputRef.current.click()}>
                <MdOutlinePhotoCamera size={20} className='cursor-pointer' />
                <input
                    id="avatarInput"
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={handleFileChange}
                    ref={avatarInputRef}
                />
            </span>
        </div>
    );
};

export default AvatarSection;
