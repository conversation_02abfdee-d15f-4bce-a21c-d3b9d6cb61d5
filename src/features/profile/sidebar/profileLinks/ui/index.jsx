import { NavLink } from "react-router-dom";
import { <PERSON>d<PERSON><PERSON>, MdLogout } from "react-icons/md";
import { IoSettingsOutline } from "react-icons/io5";
import { FaUserTie } from "react-icons/fa6";
import { AiFillWarning } from "react-icons/ai";
import { useDispatch, useSelector } from "react-redux";
import { logout } from "../../../../../store/actions/auth/Auth.js";
import { useState } from "react";
import Modal from "shared/ui/modal/Modal.jsx";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";

function PageLinks() {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const { t } = useTranslation();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const userStatus = useSelector(state => state.auth?.userStatus || "");

    const handleLogout = () => {
        dispatch(logout());
    };

    const handleIconClick = () => {
        setIsModalOpen(true);
    };

    const handleModalClose = () => {
        setIsModalOpen(false);
    };

    const handleConfirm = () => {
        setIsModalOpen(false);
        navigate('/profile/add-passport');
    };

    return (
        <div className='flex flex-col mt-5 font-medium gap-3'>
            <NavLink
                to="personal-info"
                className={({ isActive }) =>
                    `p-2 rounded flex items-center gap-2 relative ${isActive ? 'bg-gray-200' : ''} group hover:bg-gray-200`
                }
            >
                <div className='p-2'>
                    <MdHome size={20} />
                </div>
                {t('profilePage.account')}
            </NavLink>

            {/* Логика для блока "Владелец" */}
            <div className='flex items-center gap-2 '>
                {userStatus === "" ? (
                    // Если статус пустой, показываем иконку с предупреждением
                    <div className="flex items-center p-2 gap-2 cursor-pointer opacity-50" onClick={handleIconClick}>
                        <div className='p-2'>
                            <FaUserTie size={20} />
                        </div>
                        {t('profilePage.owner')}
                        <AiFillWarning size={20} color="red" />
                    </div>
                ) : (
                    // Если статус "pending" или "approve", ссылка на /owner
                    <NavLink
                        to="/owner"
                        className={({ isActive }) =>
                            `p-2 rounded flex items-center gap-2 relative ${isActive ? 'bg-gray-200' : ''} group hover:bg-gray-200 w-full
                            ${userStatus === "pending" ? 'opacity-50' : ''}`
                        }
                    >
                        <div className='p-2'>
                            <FaUserTie size={20} />
                        </div>
                        {t('profilePage.owner')}
                    </NavLink>
                )}
            </div>

            <NavLink
                to="my-bookings"
                className={({ isActive }) =>
                    `p-2 rounded flex items-center gap-2 relative ${isActive ? 'bg-gray-200' : ''} group hover:bg-gray-200`
                }
            >
                <div className='p-2'>
                    <FaUserTie size={20} />
                </div>
                {t('profilePage.myBookings')}
            </NavLink>

            <NavLink
                to="settings"
                className={({ isActive }) =>
                    `p-2 rounded flex items-center gap-2 relative ${isActive ? 'bg-gray-200' : ''} group hover:bg-gray-200`
                }
            >
                <div className='p-2'>
                    <IoSettingsOutline size={20} />
                </div>
                {t('profilePage.settings')}
            </NavLink>

            <div className='p-2 rounded flex items-center gap-2 cursor-pointer relative group'
                 onClick={handleLogout}>
                <span className='p-2'>
                    <MdLogout size={20} />
                </span>
                {t('profilePage.logout')}
            </div>

            {isModalOpen && (
                <Modal
                    isOpen={isModalOpen}
                    onClose={handleModalClose}
                    onConfirm={handleConfirm}
                    title={t('profilePage.FillPassportDetails')}
                    message={t('profilePage.message')}
                    confirmText={t('button.fillIn')}
                    cancelText={t('button.cancel')}
                    confirmButtonProps={{ className: 'px-2 py-1 bg-blue-500 rounded-md text-white w-full' }}
                />
            )}
        </div>
    );
}

export default PageLinks;
