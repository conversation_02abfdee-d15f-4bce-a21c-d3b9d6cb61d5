import React from 'react';
import { useSelector } from 'react-redux';

const ProfileInfo = () => {
    const user = useSelector(state => state.auth.user.user);
    return (
        <div className='flex flex-col justify-center items-center gap-1'>
            <span className='font-semibold text-lg'>{user?.Profile?.Nickname}</span>
            <span className='font-semibold'>{user?.Role}</span>
        </div>
    );
};

export default ProfileInfo;
