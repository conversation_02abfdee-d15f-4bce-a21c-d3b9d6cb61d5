import {useDispatch, useSelector} from "react-redux";
import { FormProvider, useForm } from "react-hook-form";
import InputField from "../../../../shared/ui/inputField/InputField.jsx";
import Button from "../../../../shared/ui/button/Button.jsx";
import {updateOwner} from "../../model/ProfileAction.js";
import {useTranslation} from "react-i18next";
import {useState, useEffect} from "react";
import {toast} from "react-toastify";
import Modal from "../../../../shared/ui/modal/Modal.jsx";
import {useNavigate} from "react-router-dom";
// import {updateOwner} from "../../model/ProfileAction.js";

function AddPassport() {
    const passport = useSelector(state => state.auth.user.user.Profile.Passport)
    const {t} = useTranslation()
    const [minExpiryDate, setMinExpiryDate] = useState("");
    const [isModalOpen, setIsModalOpen] = useState(false);
    const navigate = useNavigate()
    const today = new Date().toISOString().split("T")[0];

    const methods = useForm({
        mode: "onChange",
        defaultValues: {
            Name: passport?.Name || '',
            Surname: passport?.Surname || '',
            Patronymic: passport?.Patronymic || '',
            Nationality: passport?.Nationality || '',
            DateOfBirth: passport?.DateOfBirth || '',
            DateOfIssue: passport?.DateOfIssue || '',
            DateOfExpiry: passport?.DateOfExpiry || '',
            DocumentNumber: passport?.DocumentNumber || '',
            Authority: passport?.Authority || '',
            PIN: passport?.PIN || ''
        }
    });
    const { handleSubmit, watch, formState: { errors } } = methods;
    const dispatch= useDispatch()

    const dateOfIssue = watch("DateOfIssue");

    useEffect(() => {
        if (dateOfIssue) {
            const issueDate = new Date(dateOfIssue);
            const expiryDate = new Date(issueDate);
            expiryDate.setFullYear(issueDate.getFullYear() + 10);

            setMinExpiryDate(expiryDate.toISOString().split("T")[0]);
        } else {
            setMinExpiryDate("");
        }
    }, [dateOfIssue]);

    const onSubmit = async (data) => {
        const response = await dispatch(updateOwner(data));

        if (updateOwner.fulfilled.match(response)) {
            setIsModalOpen(true);
        } else {
            let errorMessage = "Ошибка обновления данных";
            if (response.payload.error?.Error?.includes("idx_passports_pin")) {
                errorMessage = "Паспорт с таким PIN уже зарегистрирован!";
            } else if (response.payload.error?.Error) {
                errorMessage = response.payload.Error;
            }
            toast.error(errorMessage);
            console.error("Ошибка при обновлении владельца:", response.payload);
        }
    };



    const handleModalConfirm = () => {
        setIsModalOpen(false);
        navigate('/owner')
    };


    return (
        <div className='w-full md:pl-5 lg:px-10'>
            <h2 className='font-semibold text-xl md:text-2xl text-center mb-4'>{t('addPassportInfo.passportDetails')}</h2>
            <FormProvider {...methods}>
                <form onSubmit={handleSubmit(onSubmit)} className='' noValidate>
                    <div className='grid grid-cols-1 lg:grid-cols-2 lg:gap-1 lg:gap-x-5 w-full'>
                        <InputField
                            label={t('addPassportInfo.name')}
                            name='Name'
                            type='text'
                            placeholder={t('addPassportInfo.enterName')}
                            style='bg-white md:text-base'
                            required
                            validationRules={{
                                pattern: {
                                    value: /^[A-Za-zА-Яа-яЁё\s-]+$/,
                                    message: "Можно вводить только буквы"
                                }
                            }}
                        />
                        <InputField
                            label={t('addPassportInfo.surname')}
                            name='Surname'
                            type='text'
                            placeholder={t('addPassportInfo.enterName')}
                            style='bg-white md:text-base'
                            required
                            validationRules={{
                                pattern: {
                                    value: /^[A-Za-zА-Яа-яЁё\s-]+$/,
                                    message: "Можно вводить только буквы"
                                }
                            }}
                        />
                        <InputField
                            label={t('addPassportInfo.patronymic')}
                            name='Patronymic'
                            type='text'
                            placeholder={t('addPassportInfo.enterName')}
                            style='bg-white md:text-base'
                            validationRules={{
                                pattern: {
                                    value: /^[A-Za-zА-Яа-яЁё\s-]+$/,
                                    message: "Можно вводить только буквы"
                                }
                            }}
                        />
                        <InputField
                            label={t('addPassportInfo.nationality')}
                            name='Nationality'
                            type='text'
                            placeholder={t('addPassportInfo.enterNationality')}
                            style='bg-white md:text-base'
                            required
                            validationRules={{
                                pattern: {
                                    value: /^[A-Za-zА-Яа-яЁё\s-]+$/,
                                    message: "Можно вводить только буквы"
                                }
                            }}
                        />
                        <InputField
                            label={t('addPassportInfo.dateOfBirth')}
                            name='DateOfBirth'
                            type='date'
                            placeholder={t('addPassportInfo.enterDateBirth')}
                            style='bg-white md:text-base'
                            required
                            min="1930-01-01"
                            max={new Date(new Date().setFullYear(new Date().getFullYear() - 18)).toISOString().split("T")[0]}
                            validationRules={{
                                required: "Дата рождения обязательна",
                                validate: (value) => {
                                    if (!value) return "Дата рождения обязательна";

                                    const birthDate = new Date(value);
                                    const today = new Date();

                                    const minAllowedDate = new Date();
                                    minAllowedDate.setFullYear(today.getFullYear() - 18);

                                    // Проверяем, не младше ли 18 лет
                                    if (birthDate > minAllowedDate) {
                                        return "Вам должно быть не менее 18 лет";
                                    }

                                    return true;
                                }
                            }}
                        />

                        <InputField
                            label={t('addPassportInfo.documentNumber')}
                            name='DocumentNumber'
                            type='string'
                            placeholder={t('addPassportInfo.documentNumber')}
                            style='bg-white md:text-base'
                            required
                            validationRules={{
                                pattern: {
                                    value: /^[0-9]+$/,
                                    message: "Должен содержать только цифры"
                                }
                            }}
                        />

                        <InputField
                            label={t('addPassportInfo.dateOfIssue')}
                            name='DateOfIssue'
                            type='date'
                            placeholder={t('addPassportInfo.enterDate')}
                            style='bg-white md:text-base'
                            required
                            max={today} // Дата выдачи не может быть в будущем
                            validationRules={{
                                required: "Дата выдачи обязательна",
                                validate: (value) => {
                                    if (!value) return "Дата выдачи обязательна";
                                    if (value > today) return "Дата выдачи не может быть в будущем";
                                    return true;
                                }
                            }}
                        />

                        <InputField
                            label={t('addPassportInfo.dateOfExpiry')}
                            name='DateOfExpiry'
                            type='date'
                            placeholder={t('addPassportInfo.enterDate')}
                            style='bg-white md:text-base'
                            required
                            min={minExpiryDate} // Минимальная дата — 10 лет от даты выдачи
                            validationRules={{
                                required: "Дата окончания обязательна",
                                validate: (value) => {
                                    if (!value) return "Дата окончания обязательна";
                                    if (value < today) return "Проверьте срок действия паспорта";
                                    if (dateOfIssue && value < minExpiryDate) {
                                        return "Дата окончания должна быть минимум на 10 лет больше даты выдачи";
                                    }
                                    return true;
                                }
                            }}
                        />


                        <InputField
                            label={t('addPassportInfo.authority')}
                            name='Authority'
                            type='text'
                            placeholder={t('addPassportInfo.enterAuthority')}
                            style='bg-white md:text-base'
                            required
                        />
                        <InputField
                            label={t('addPassportInfo.pin')}
                            name='PIN'
                            type='string'
                            placeholder={t('addPassportInfo.enterDocNumber')}
                            style='bg-white md:text-base'
                            required
                            validationRules={{
                                required: "PIN обязателен",
                                minLength: {
                                    value: 11,
                                    message: "PIN должен содержать не менее 11 символов"
                                },
                                maxLength: {
                                    value: 13,
                                    message: "PIN должен содержать менее 14 символов"
                                },
                                pattern: {
                                    value: /^[0-9]+$/,
                                    message: "PIN должен содержать только цифры"
                                }
                            }}
                        />
                    </div>
                    <div className='flex justify-center md:mt-5'>
                        <Button
                            name={t('button.save')}
                            style='px-5 py-2 bg-blue-500 text-white font-medium hover:bg-blue-400'
                        />
                    </div>
                </form>
            </FormProvider>
            <Modal
                title={t('addPassportInfo.modalInfo')}
                message={t('modal.successMessageAddPassport')}
                isOpen={isModalOpen}
                onConfirm={handleModalConfirm}
                buttonHidde={false}
                style='flex flex-col items-center'
            >
                <Button
                    name={t('button.close')}
                    onClick={handleModalConfirm}
                    style='bg-btn-blue text-white '
                />
            </Modal>
        </div>
    );
}

export default AddPassport;