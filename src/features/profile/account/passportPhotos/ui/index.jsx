import { useFormContext } from 'react-hook-form';
import { RiDeleteBinLine } from 'react-icons/ri';
import ImagesInput from 'shared/ui/inputField/ImagesInput.jsx';
import { handleImageUpload } from 'shared/utils/imageLoadUtils.js';

const PassportPhotos = ({ documentType, handleClearImage, setValue }) => {
    const { watch } = useFormContext();

    const idPhotos = [
        { type: 'idPassportPhotoFront', label: 'Загрузите лицевую сторону' },
        { type: 'idPassportPhotoBack', label: 'Загрузите оборотную сторону' },
        { type: 'idPassportPhotoWithClient', label: 'Загрузите фото с документом' }
    ];

    const passportPhotos = [
        { type: 'internationalPassportPhoto', label: 'Загрузите фото паспорта' },
        { type: 'internationalPassportPhotoWithClient', label: 'Загрузите фото c паспортом' }
    ];

    const photos = documentType === 'idCard' ? idPhotos : passportPhotos;

    return (
        <div className='flex flex-col items-center lg:flex-row flex-wrap md:flex gap-5'>
            {photos.map(photo => {
                const photoValue = watch(photo.type);
                return photoValue ? (
                    <div key={photo.type} className='relative flex justify-center w-[250px] h-[150px]'>
                        <img
                            src={photoValue.previewURL}
                            alt={photo.label}
                            className="object-cover max-w-full max-h-full" />
                        <p className='absolute right-1 top-1 font-bold text-red-600 cursor-pointer' onClick={() => handleClearImage(photo.type)}>
                            <RiDeleteBinLine size={20} />
                        </p>
                    </div>
                ) : (
                    <ImagesInput
                        key={photo.type}
                        label={photo.label}
                        name={photo.type}
                        id={photo.type}
                        onImageUpload={(e) => handleImageUpload(e, (file) => setValue(photo.type, file))}
                        style='flex items-center justify-center border-dotted h-[150px] w-[250px]'
                    />
                )
            })}
        </div>
    );
};

export default PassportPhotos;
