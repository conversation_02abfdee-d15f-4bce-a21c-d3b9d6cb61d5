import { useFormContext } from 'react-hook-form';
import {useTranslation} from "react-i18next";

const DocumentTypeSelector = () => {
    const {t}= useTranslation()
    const methods = useFormContext();
    return (
        <div className="flex flex-col mb-2 w-max">
            <label htmlFor='documentType' className='mb-2 font-bold text-base'>{t('account.photoDocument')} *</label>
            <select
                name='documentType'
                id='documentType'
                className='p-2 border border-gray-300 rounded focus:outline-none w-100'
                {...methods.register('documentType')}
            >
                <option value=''>{t('account.selectDocumentType')}</option>
                <option value='idCard'>{t('account.idCard')}</option>
                <option value='passport'>{t('account.foreignPassport')}</option>
            </select>
        </div>
    );
};

export default DocumentTypeSelector;
