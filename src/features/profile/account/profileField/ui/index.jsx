import { useFormContext } from 'react-hook-form';
import Button from 'shared/ui/button/Button.jsx';
import InputField from 'shared/ui/inputField/InputField.jsx';
import {useTranslation} from "react-i18next";

const ProfileField = ({ field, isEditing, editField, handleEditClick, handleCancel, handleSubmit, handleSave }) => {
    const {t} = useTranslation()
    const methods = useFormContext();
    return (
        <div className="flex flex-col border-b pb-3">
            <label className="font-bold mb-1 text-base">
                {field === 'Nickname' ? t('account.name') : field === 'Email' ? t('account.emailAddress') : t('account.phoneNumber')}
            </label>
            {isEditing && editField === field ? (
                <form onSubmit={handleSubmit(handleSave)} className="flex flex-col gap-2">
                    <InputField
                        name={field}
                        type={field === 'Email' ? 'Email' : field === 'PhoneNumber' ? 'phone' : 'text'}
                        placeholder={field === 'Nickname' ? 'Введите имя' : field === 'Email' ? 'Введите электронный адрес' : 'Введите номер телефона'}
                        required
                    />
                    <div className="flex gap-2">
                        <Button type='submit' style='bg-blue-500 text-white' name={t('button.save')} />
                        <Button type='button' style='bg-gray-500 text-white' name={t('button.cancel')} onClick={handleCancel} />
                    </div>
                </form>
            ) : (
                <div className="flex justify-between">
                    <span className="md:text-lg">{methods.getValues(field) || 'нет данных'}</span>
                    <span onClick={() => handleEditClick(field)} className="text-sm md:text-base cursor-pointer">{t('account.edit')}</span>
                </div>
            )}
        </div>
    );
};

export default ProfileField;
