import { createSlice } from "@reduxjs/toolkit";
import {fetchUserProfile} from "./ProfileAction.js";
import createGenericBuilder from "../../../shared/libs/BuilderGeneric/BuilderGeneric.js";



const initialState = {
    single: {},
    users: {},
    status: 'idle',
    error: ''
};

const profileSlice = createSlice({
    name: 'profile',
    initialState,
    reducers: {},
    extraReducers: (builder) => {
        const genericBuilder = createGenericBuilder(builder, fetchUserProfile);
        genericBuilder
            .fulfilled(fetchUserProfile, (state, action) => {
                state.user = action.payload;
            })
        genericBuilder.includeCases();
    }
});

export default profileSlice.reducer;
