import { createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import axiosInstance from "../../../shared/libs/AxiosInstance/index.js";
import {toast} from "react-toastify";


const Api = import.meta.env.VITE_REACT_API_URL;

export const fetchUserProfile = createAsyncThunk(
    'user/fetchUserProfile',
    async ({ id }, { rejectWithValue }) => {
        try {
            const response = await axios.get(`${Api}api/auth/${id}`);
            return response.data.data;
        } catch (error) {
            return rejectWithValue(error.response ? error.response.data : error.message);
        }
    }
);


export const updateUserInfo = createAsyncThunk(
    'user/updateUserInfo',
    async ( data , { rejectWithValue }) => {
        // const {} = args
        try {
            const response = await axiosInstance.patch(`${Api}api/auth`, data);
            toast.success("Данные успешно отправлены");
            return response.data;
        } catch (error) {
            toast.error("Ошибка при отправке данных")
            return rejectWithValue(error.response ? error.response.data : error.message);
        }
    }
);

export const updateUserPassportImages = createAsyncThunk(
    'user/updateUserPassportImages',
    async ( data , { rejectWithValue }) => {
        try {
            const response = await axiosInstance.patch(`${Api}api/auth/change_photo`, data, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });
            toast.success("Данные успешно обновлены")
            return response.data;
        } catch (error) {
            toast.error('Ошибку при оправке данных')
            return rejectWithValue(error.response ? error.response.data : error.message);
        }
    }
);


export const updateUserAvatar = createAsyncThunk(
    'auth/updateUserAvatar',
    async ({ file }, { rejectWithValue }) => {
        try {
            const formData = new FormData();
            formData.append('Avatar', file);
            const response = await axiosInstance.patch(`${Api}api/auth/change_photo`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response ? error.response.data : error.message);
        }
    }
);


export const updateOwner = createAsyncThunk(
    'auth/updateOwner',
    async (data, { rejectWithValue }) => {
        try {
            const response = await axiosInstance.patch(`${Api}api/auth/be_owner`, data);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response ? error.response.data : error.message);
        }
    }
);