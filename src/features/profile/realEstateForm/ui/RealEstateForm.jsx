    import { FormProvider, useForm } from "react-hook-form";
    import Button from "shared/ui/button/Button";
    import SelectInput from "shared/ui/inputField/SelectInput";
    import inputData from "../model/inputData";
    import useRealEstateForm from "../model/useRealEstateForm";
    import MultiImageInputs from "./MultiImageInputs.jsx";
    import {useTranslation} from "react-i18next";
    import InputData from "../model/inputData";
    import {useEffect} from "react";

    const RealEstateForm = ({ edit = false, id = null }) => {
        const methods = useForm({
            defaultValues: {
                Photos: [],
                Facilities: [],
                Rules: []
            }
        });
        const { t } = useTranslation();
        const { handleSubmit } = methods;
        const { onSubmit, handleImageRemove } = useRealEstateForm({ edit, id, methods });
        const inputData = InputData();

        return <FormProvider {...methods}>
            <form onSubmit={handleSubmit(onSubmit)} >
                <div className='flex flex-col lg:flex-row lg:gap-10 justify-between mb-10'>
                    <div className='lg:w-1/2'>
                        {inputData.Title}
                        {inputData.CategoryID}
                        {inputData.Description}
                       <div className='flex flex-col md:flex-row md:gap-10'>
                           {inputData.PeopleQuantity}
                           {inputData.RoomsQuantity}
                       </div>
                        {inputData.Price}
                        {/*{inputData.DiscountPrice}*/}
                        {inputData.PriceDescription}
                        {inputData.Facilities}
                        {inputData.Rules}
                    <div className='flex flex-col md:flex-row md:gap-10'>
                        {inputData.CheckInID}
                        {inputData.CheckOutID}
                    </div>
                    </div>
                    <div className='flex flex-col gap-5 w-full lg:w-1/2'>
                        {inputData.Location}
                        <MultiImageInputs
                            label={t('createAccommodation.addPhoto')}
                            name="Photos"
                            handleImageRemove={handleImageRemove}
                        />
                    </div>
                </div>
               <div className='w-full'>
                   <Button
                       type="submit"
                       name={`${edit ? t('createAccommodation.editData') :  t('createAccommodation.createAd')}`}
                       style='px-4 py-2 bg-btn-border-blue text-white '
                   />
               </div>
            </form>
        </FormProvider>
    }

    export default RealEstateForm;