import { useFieldArray, useFormContext } from 'react-hook-form';
import { IconButton } from '@mui/material';
import ImageUploadInput from "../../../../shared/ui/inputField/ImageUploadInput.jsx";
import { useTranslation } from "react-i18next";

const MultiImageInputs = ({ label, name = "Photos", handleImageRemove }) => {
    const { control, getValues } = useFormContext();
    const { fields: images, append, remove, move } = useFieldArray({ control, name });
    const { t } = useTranslation();

    const handleImageUpload = (newImages) => {
        const files = Array.from(newImages);
        files.forEach(file => {
            append({ file, isExisting: false });
        });
    };

    const handleRemove = (index) => {
        const imageValues = getValues(name);
        const removedImage = imageValues[index];
        if (removedImage.isExisting && handleImageRemove) {
            handleImageRemove(removedImage.id);
        }
        remove(index);
    };

    const handleSetMainPhoto = (index) => {
        // Move selected image to the first position
        move(index, 0);
    };

    return (
        <div>
            <label className='font-bold mb-2 text-base md:text-lg'>{label}</label>
            <div className='grid grid-cols-2 xl:grid-cols-3 gap-4'>
                {images.map((item, index) => (
                    <div
                        key={item.id || index}
                        className={`relative group ${index === 0 ? 'border-4 border-green-500' : ''} rounded-xl`}
                    >
                        <ImageUploadInput
                            file={item.file}
                            url={item.url}
                            onRemove={() => handleRemove(index)}
                        />
                        {/* On larger screens, display the button on hover; on smaller screens, display it permanently */}
                        <p
                            onClick={() => handleSetMainPhoto(index)}
                            className={`absolute bottom-2 right-2 px-2 py-1 rounded bg-btn-border-blue text-white text-sm
                                md:opacity-0 md:transition-opacity md:duration-200 md:ease-in-out md:group-hover:opacity-100 cursor-pointer
                                ${index === 0 ? 'hidden' : ''} ${index !== 0 ? 'block' : 'md:hidden'}`}
                        >
                            {t('createAccommodation.selectMainImage')}
                        </p>
                        {index === 0 && (
                            <div className="absolute top-0 md:top-2 md:left-2 bg-green-500 text-white text-xs px-2 py-1 md:rounded">
                                {t('createAccommodation.mainImage')}
                            </div>
                        )}
                    </div>
                ))}
                <IconButton
                    component="label"
                    style={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        border: '2px dashed #ccc',
                        borderRadius: '8px',
                        height: '192px'
                    }}
                >
                    <span style={{ fontSize: '16px', color: '#999' }}>{t('createAccommodation.addPhoto')}</span>
                    <input
                        type="file"
                        accept="image/*"
                        hidden
                        multiple
                        onChange={(event) => handleImageUpload(event.target.files)}
                    />
                </IconButton>
            </div>
        </div>
    );
};

export default MultiImageInputs;
