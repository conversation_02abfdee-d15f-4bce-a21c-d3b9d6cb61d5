import { useEffect,useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { API_IMG } from "shared/config/api";
import { createAccommodation, editAccommodation, getAccommodationByID } from "store/actions/accommodations/Accommodation";
import { logout } from "../../../../store/actions/auth/Auth.js";
import inputFieldNames from "../../../../shared/utils/inputFieldsName.js";

const useRealEstateForm = ({ edit, id, methods }) => {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const [imagesToDelete, setImagesToDelete] = useState([]);

    useEffect(() => {
        if (edit) {
            dispatch(getAccommodationByID(id));
        }
    }, [id, edit, dispatch]);

    const accommodationByID = useSelector(
        (state) => state.accommodations.accommodationByID[id]?.Accommodation
    );
    useEffect(() => {
        const initializeForm = async () => {
            if (edit && accommodationByID?.ID) {
                const data = {};
                inputFieldNames.forEach((key) => {
                    data[key] = accommodationByID[key];
                });
                data.Photos = accommodationByID.Images.map((photo) => ({
                    id: photo.ID,
                    url: `${API_IMG}${photo.ImageUrl}`,
                    isExisting: true,
                }));

                data.ID = accommodationByID.ID;
                data.Location = {
                    address: accommodationByID.LocationLabel,
                    lat: accommodationByID.Latitude,
                    lng: accommodationByID.Longitude,
                    city: accommodationByID.City,
                    country: accommodationByID.Country,
                };
                data.Facilities = data?.Facilities?.map((item) => item.ID);
                data.Rules = data?.Rules?.map((item) => item.ID);
                methods.reset(data);
            }
        };
        initializeForm();
    }, [accommodationByID, edit, methods]);


    const handleImageRemove = (imageId) => {
        setImagesToDelete((prev) => [...prev, imageId]);
    };

    const onSubmit = async (data) => {
        console.log(data, 'DATA')
        if (!data.Location) {
            toast.error("Укажите местоположение");
            return;
        }

        if (data.Photos.length === 0){
            toast.error('Необходимо добавить фотографии!')
            return;
        }

        if (data.Photos.length < 5) {
            toast.error('Необходимо добавить не менее 5 фотографий!');
            return;
        }

        Object.keys(data).forEach(key => {
            if (data[key] === "") {
                delete data[key];

            }
        });

        // Prepare location data
        data.Longitude = String(data.Location.lng);
        data.Latitude = String(data.Location.lat);
        data.LocationLabel = data.Location.address;
        data.City = data.Location.city;
        data.Country = data.Location.country;

        delete data.address;
        delete data.city;
        delete data.country;
        delete data.Location

        // Separate new and existing photos
        const newPhotos = data.Photos.filter(photo => !photo.isExisting).map(photo => ({ file: photo.file }));
        const existingPhotoIDs = data.Photos.filter(photo => photo.isExisting).map(photo => photo.id);
        data.Photos = newPhotos;
        data.ExistingPhotoIDs = existingPhotoIDs;

        if (data.Photos.length > 0) {
            data.MainPhoto = data.Photos[0]; // Первое фото среди новых фото считается основным
        } else if (existingPhotoIDs.length > 0) {
            data.MainPhotoID = existingPhotoIDs[0]; // Если нет новых фото, основное фото из существующих
        }


        if (edit) {
            await dispatch(editAccommodation({ data, id, imagesToDelete }));
        } else {
            await dispatch(createAccommodation({ data }));
        }

        navigate(-1);
    };

    return {
        handleImageRemove,
        onSubmit
    };
};

export default useRealEstateForm;
