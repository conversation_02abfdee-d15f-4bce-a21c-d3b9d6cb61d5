import GeolocationInput from "shared/ui/inputField/GeolocationInput";
import InputField from "shared/ui/inputField/InputField";
import SelectInput from "shared/ui/inputField/SelectInput";
import MultiImageInputs from "../ui/MultiImageInputs";
import { useTranslation } from "react-i18next";
import TextField from "../../../../shared/ui/inputField/TextField.jsx";

const InputData = () => {
    const { t } = useTranslation();

    return {
        Title: <InputField label={t('createAccommodation.name')} name="Title" placeholder={t('createAccommodation.name')} required />,
        CategoryID: <SelectInput name="CategoryID" label={t('createAccommodation.category')} url="dictionary/categories/get_all" mapFunction={(data) => ({ id: data.Id, title: data.Name })} />,
        Description: <TextField label={t('createAccommodation.description')} name="Description" placeholder={t('createAccommodation.description')} required rows={10} />,
        Photos: <MultiImageInputs label={t('createAccommodation.uploadPhotos')} name="Photos" />,
        PeopleQuantity: <InputField type="number" min={0}  label={t('createAccommodation.numberOfGuests')} name="PeopleQuantity" placeholder={t('createAccommodation.numberOfGuests')} required />,
        RoomsQuantity: <InputField type="number" min={0}  label={t('createAccommodation.numberOfRooms')} name="RoomsQuantity" placeholder={t('createAccommodation.numberOfRooms')} required />,
        Location: <GeolocationInput name="Location" />,
        Price: <InputField type="number" min={0} label={t('createAccommodation.pricePerDay')} name="Price" placeholder={t('createAccommodation.pricePerDay')} required />,
        PriceDescription: <InputField label={t('createAccommodation.pricingDescription')} name="PriceDescription" placeholder={t('createAccommodation.pricePerDay')} />,
        DiscountPrice: <InputField type="number" label={t('createAccommodation.discount')} name="DiscountPrice" placeholder={t('createAccommodation.discount')} />,
        Facilities: <SelectInput multi name="Facilities" label={t('createAccommodation.facilities')} url="dictionary/facilities/get_all" mapFunction={(data) => ({ id: data.Id, title: data?.Value })} />,
        Rules: <SelectInput multi name="Rules" label={t('createAccommodation.rules')} url="dictionary/rules/get_all" mapFunction={(data) => ({ id: data.Id, title: data?.Value })} />,
        CheckInID: <SelectInput name="CheckInID" label={t('createAccommodation.checkInTime')} url="dictionary/check_in_out/" mapFunction={(data) => ({ id: data?.ID, title: data?.Name })} required={t('createAccommodation.checkInTime')} />,
        CheckOutID: <SelectInput name="CheckOutID" label={t('createAccommodation.checkOutTime')} url="dictionary/check_in_out/" mapFunction={(data) => ({ id: data?.ID, title: data?.Name })} required={t('createAccommodation.checkOutTime')} />
    };
};

export default InputData;
