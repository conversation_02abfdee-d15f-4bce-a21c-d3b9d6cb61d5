import React, { useState } from 'react';
import {useTranslation} from "react-i18next";

function SelectionRoomsAndBeds({roomsQuantity, setRoomsQuantity, peopleQuantity, setPeopleQuantity }) {
    const {t} = useTranslation()


    const roomOptions = [1, 2, 3, 4, 5];
    const bedOptions = [1, 2, 3, 4, 5];

    return (
        <div className="md:absolute top-full left-0 mt-2 mb-5 md:bg-white md:shadow-lg md:border md:rounded-md md:p-4 z-10 md:w-max">
            <div className='flex flex-col justify-between md:flex-col'>
                <div className=" mb-3 md:mb-6">
                    <div className="text-sm font-semibold mb-2">{t('filter.rooms')}</div>
                    <div className="grid grid-cols-5 gap-2 w-max">
                        {roomOptions.map((option, index) => (
                            <button
                                key={index}
                                onClick={() => setRoomsQuantity(option)}
                                className={`text-sm py-2 px-3 border rounded-lg ${
                                    roomsQuantity === option ? 'bg-lock-orange text-white' : 'border-lock-orange '
                                }`}
                            >
                                {option === 5 ? '5+' : option}
                            </button>
                        ))}
                    </div>
                </div>
                <div>
                    <div className="text-sm font-semibold mb-2">{t('filter.beds')}</div>
                    <div className="grid grid-cols-5 gap-2 w-max">
                        {bedOptions.map((option, index) => (
                            <button
                                key={index}
                                onClick={() => setPeopleQuantity(option)}
                                className={`text-sm py-2 px-3 border rounded-lg ${
                                    peopleQuantity === option ? 'bg-lock-orange text-white' : 'border-lock-orange '
                                }`}
                            >
                                {option === 5 ? '5+' : option}
                            </button>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    );
}

export default SelectionRoomsAndBeds;
