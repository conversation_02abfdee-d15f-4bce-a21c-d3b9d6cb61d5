    import React from 'react';

    function RatingFilter({setSelectedRating, selectedRating}) {
        const ratings = [1,2,3,4,5];

        return (
            <div
                className="md:absolute top-full left-0 mt-5 md:mt-2 md:bg-white md:shadow-lg md:border md:rounded-md md:p-4 z-10 md:w-max">
                <h2 className='block md:hidden text-sm font-semibold mb-2'>По ценовому диапазону</h2>
                <div className="grid grid-cols-5 gap-2 w-max">
                    {ratings.map((option, index) => (
                        <button
                            key={index}
                            onClick={() => setSelectedRating(option)}
                            className={`text-sm py-2 px-3 border rounded-lg ${
                                selectedRating === option ? 'bg-lock-orange text-white' : 'border-lock-orange'
                            }`}
                        >
                            {option === 5 ? '5+' : option}
                        </button>
                    ))}
                </div>
            </div>
        );
    }

    export default RatingFilter;