import React, {useEffect, useState} from 'react';
import {useGetHandbookQuery} from "../../../../store/slices/handbook/handbookSlice.js";
import {useDispatch} from "react-redux";
import {useTranslation} from "react-i18next";

function Facilities({ updateSelectedFacilities, selectedFacilities }) {
    const { data, isLoading = false } = useGetHandbookQuery('dictionary/facilities/get_all');
    const {t} = useTranslation()
    const toggleFacility = (id) => {
        const updatedFacilities = selectedFacilities.includes(id)
            ? selectedFacilities.filter(facility => facility !== id)
            : [...selectedFacilities, id];
        updateSelectedFacilities(updatedFacilities);
    };


    return (
        <div
            className="md:absolute top-full -left-32 md:mt-2 md:bg-white md:shadow-lg md:border md:rounded-md md:p-4 z-10 md:w-max">
            <h3 className="font-semibold text-sm mb-2 ">{t('filter.selectAmenities')}</h3>
            <div className="grid md:grid-cols-2 gap-x-5 gap-y-1">
                {data && data.map(facility => (
                    <div key={facility.Id} className="flex items-center space-x-2 ">
                        <input
                            type="checkbox"
                            id={`facility-${facility.Id}`}
                            checked={selectedFacilities.includes(facility.Id)}
                            onChange={() => toggleFacility(facility.Id)}
                            className="
                                                    appearance-none cursor-pointer h-4 w-4 border border-amber-500 rounded-sm
                                                    checked:bg-amber-500 checked:border-transparent
                                                    focus:outline-none focus:ring-offset-0 focus:ring-1 focus:ring-amber-400"
                        />
                        <label htmlFor={`facility-${facility.Id}`} className="cursor-pointer text-sm text-blue-text-lock font-medium">
                            {facility.Value}
                        </label>
                    </div>
                ))}
            </div>
        </div>
    );
}

export default Facilities;