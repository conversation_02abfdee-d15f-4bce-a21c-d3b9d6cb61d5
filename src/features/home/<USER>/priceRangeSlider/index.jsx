import React, { useEffect, useState } from 'react';
import {useTranslation} from "react-i18next";

function PriceRangeSlider({minPrice, setMinPrice, maxPrice, setMaxPrice}) {
    const {t} = useTranslation()
    const min = 200;
    const max = 10000;

    const [minThumb, setMinThumb] = useState(0);
    const [maxThumb, setMaxThumb] = useState(0);

    const clampValue = (value, min, max) => Math.max(min, Math.min(value, max));

    const minTrigger = (value) => {
        const clampedValue = clampValue(value, min, maxPrice - 100);
        setMinPrice(clampedValue);
    };

    const maxTrigger = (value) => {
        const clampedValue = clampValue(value, minPrice + 100, max);
        setMaxPrice(clampedValue);
    };

    useEffect(() => {
        setMinThumb(((minPrice - min) / (max - min)) * 100);
        setMaxThumb(100 - (((maxPrice - min) / (max - min)) * 100));
    }, [minPrice, maxPrice]);

    const handleThumbMove = (e, thumb) => {
        const slider = e.target.parentElement;
        const sliderWidth = slider.clientWidth;

        // Обработка событий касания
        const clientX = e.touches ? e.touches[0].clientX : e.clientX;
        const newX = clientX - slider.getBoundingClientRect().left;
        const value = Math.round((newX / sliderWidth) * (max - min)) + min;

        if (thumb === "min") {
            minTrigger(value);
        } else {
            maxTrigger(value);
        }
    };

    const handleMouseDown = (e, thumb) => {
        const onMouseMove = (event) => handleThumbMove(event, thumb);
        const onMouseUp = () => {
            document.removeEventListener('mousemove', onMouseMove);
            document.removeEventListener('mouseup', onMouseUp);
            document.removeEventListener('touchmove', onMouseMove); // Удаляем touchmove
            document.removeEventListener('touchend', onMouseUp); // Удаляем touchend
        };

        document.addEventListener('mousemove', onMouseMove);
        document.addEventListener('mouseup', onMouseUp);
        document.addEventListener('touchmove', onMouseMove); // Добавляем touchmove
        document.addEventListener('touchend', onMouseUp); // Добавляем touchend
    };

    return (
        <div
            className="md:absolute top-full -left-2.5 md:mt-2 bg-white md:shadow-lg md:border md:rounded-md md:p-4 z-10 w-full md:w-max">
            <h2 className='block md:hidden text-sm font-semibold mb-2'>{t('filter.byPriceRange')}</h2>
            <div className="flex justify-center items-center mt-5 md:mt-10 md:px-5 text-sm">
                <div className="relative md:max-w-xl md:w-full">
                    <div className='px-2 sm:px-0'>
                        {/* Ползунки невидимы и не реагируют на клики */}
                        <input
                            type="range"
                            step="100"
                            min={min}
                            max={max}
                            value={minPrice}
                            readOnly
                            className="absolute pointer-events-none appearance-none z-20 h-2 w-full opacity-0"
                        />
                        <input
                            type="range"
                            step="100"
                            min={min}
                            max={max}
                            value={maxPrice}
                            readOnly
                            className="absolute pointer-events-none appearance-none z-20 h-2 w-full opacity-0"
                        />
                        {/* Трек и заполняющий бар */}
                        <div className="relative z-10 h-2">
                            <div
                                className="absolute z-10 left-0 right-0 h-1 bottom-0 top-0.5 rounded-md bg-gray-200"></div>
                            <div
                                className="absolute z-20 top-0.5 bottom-0 h-1 rounded-md bg-lock-orange"
                                style={{left: `${minThumb}%`, right: `${maxThumb}%`}}
                            ></div>
                            {/* Минимальный круг */}
                            <div
                                className="absolute z-30 w-4 h-4 top-0 bg-lock-orange rounded-full -mt-1 -ml-3 cursor-pointer shadow-xl"
                                style={{left: `${minThumb}%`, pointerEvents: 'auto'}}
                                onMouseDown={(e) => handleMouseDown(e, 'min')}
                                onTouchStart={(e) => handleMouseDown(e, 'min')} // Добавляем обработку касания
                            ></div>

                            {/* Максимальный круг */}
                            <div
                                className="absolute z-30 w-4 h-4 top-0 bg-lock-orange rounded-full -mt-1 -mr-3 cursor-pointer shadow-xl"
                                style={{right: `${maxThumb}%`, pointerEvents: 'auto'}}
                                onMouseDown={(e) => handleMouseDown(e, 'max')}
                                onTouchStart={(e) => handleMouseDown(e, 'max')} // Добавляем обработку касания
                            ></div>
                        </div>
                    </div>

                    {/* Поля ввода только для чтения */}
                    <div className="flex justify-between items-center py-5 gap-2 md:gap-4">
                        <div className="flex flex-col items-center">
                            <label>{t('filter.minPrice')}</label>
                            <input
                                type="number"
                                value={minPrice}
                                readOnly
                                className="px-3 py-2 border border-gray-200 rounded text-center cursor-default w-2/3"
                            />
                        </div>
                        <div className="flex flex-col items-center">
                            <label>{t('filter.maxPrice')}</label>
                            <input
                                type="number"
                                value={maxPrice}
                                readOnly
                                className="px-3 py-2 border border-gray-200 rounded text-center cursor-default w-2/3"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default PriceRangeSlider;
