import React, {useEffect, useRef, useState} from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {AppDispatch, RootState} from "../../../../app/store";
import {clearAccommodations} from "../../../../store/slices/accommodation/accommodationSlice";
import {getAccommodations} from "../../../../store/actions/accommodations/Accommodation";
import {useScrollPagination} from "../../../../shared/hooks/useScrollPagination";
import CategoryFilter from "../../categoryFilter/ui";
import Cards from "../../../../entities/homeAccommodationCard/ui/Card";
import FacilitiesFilter from "../../facilitiesFilter/ui";
import SkeletonMainCard from "../../../../shared/ui/skeleton/mainAccommodationCardSkeleton";
import {fetchProfile} from "../../../../store/actions/auth/Auth";
import {selectIsModerator} from "../../../../store/selectors/authRole";
import AppDownloadBanner from "../../../appDownloadBanner/ui/AppDownloadBanner";
import MapSearch from "../../../../widgets/mapSearch/MapSearch";
import {setMapVisible} from "../../../../store/slices/uiSlice/uiSlice";
import {useOutsideClick} from "../../../../shared/hooks/useOutsideClick";

const AccommodationList: React.FC = () => {
    const dispatch = useDispatch<AppDispatch>();
    const mapRef = useRef(null);
    const isModerator = useSelector(selectIsModerator)
    const accommodations = useSelector((state: RootState) => state.accommodations.accommodations);
    const searchTerm = useSelector((state) => state.accommodations.searchTerm);
    const hasMore = useSelector((state: RootState) => state.accommodations.hasMore);
    const [selectedCategoryId, setSelectedCategoryId] = useState<string | null>(null);
    const [selectedCategory, setSelectedCategory] = useState<number | null>(null);
    const [page, setPage] = useState(1);
    const [isLoading, setIsLoading] = useState(false);
    const limit = 15;
    const [filters, setFilters] = useState({
        selectedFacilities: [],
        minPrice: null,
        maxPrice: null,
        roomsQuantity: null,
        peopleQuantity: null,
        selectedRating: null,

    });
    const [mapFiltered, setMapFiltered] = useState([]);
    const isMapVisible = useSelector((state: RootState) => state.ui.isMapVisible);

    useOutsideClick(mapRef, () => dispatch(setMapVisible(false)), isMapVisible);

    useEffect(() => {
        dispatch(clearAccommodations());
        setPage(1);
    }, [dispatch, selectedCategory, filters,searchTerm ]);

    useEffect(() => {
        if (!isModerator) {
            dispatch(fetchProfile());
        }
    }, [dispatch, isModerator]);

    useEffect(() => {
        const loadRooms = async () => {
            setIsLoading(true);
            const filterParams = {
                title: searchTerm,
                page,
                limit,
                category_id: selectedCategory,
                min_price: filters.minPrice,
                max_price: filters.maxPrice,
                facilities: filters.selectedFacilities.length > 0 ? filters.selectedFacilities : null,
                rooms_quantity: filters.roomsQuantity,
                people_quantity: filters.peopleQuantity,
                min_rating: filters.selectedRating,
            };
            await dispatch(getAccommodations(filterParams));
            setIsLoading(false);
        };
        loadRooms();
    }, [dispatch, page, selectedCategory, filters, searchTerm]);

    useEffect(() => {
        const loadAllForMap = async () => {
            const filterParams = {
                title: searchTerm,
                page: 1,
                limit: 9999, // или API без лимита
                category_id: selectedCategory,
                min_price: filters.minPrice,
                max_price: filters.maxPrice,
                facilities: filters.selectedFacilities.length > 0 ? filters.selectedFacilities : null,
                rooms_quantity: filters.roomsQuantity,
                people_quantity: filters.peopleQuantity,
                min_rating: filters.selectedRating,
            };

            const res = await dispatch(getAccommodations(filterParams)).unwrap();
            setMapFiltered(res?.Data || []);
        };

        if (isMapVisible) {
            loadAllForMap();
        }
    }, [isMapVisible, selectedCategory, filters, searchTerm, dispatch]);


    useScrollPagination({
        isLoading,
        hasMore,
        onLoadMore: () => setPage((prevPage) => prevPage + 1),
    });

    const handleApplyFilters = (newFilters) => {
        // Сравниваем новые фильтры с текущими
        const filtersChanged = JSON.stringify(filters) !== JSON.stringify(newFilters);

        if (filtersChanged) {
            setFilters(newFilters);
        }
    };

    const resetCategory = () => {
        setSelectedCategory(null);
        setSelectedCategoryId(null)
    };


    return (
        <div>
            <div className='sticky  top-0 z-30 bg-white -mr-0.5'>
                <div className='md:flex justify-center'>
                    <CategoryFilter
                        onSelectCategory={setSelectedCategory}
                        selectedCategoryId={selectedCategoryId}
                        setSelectedCategoryId={setSelectedCategoryId}
                    />
                </div>
                <div className='relative'>
                    <FacilitiesFilter
                        onApplyFilters={handleApplyFilters}
                        resetCategory={resetCategory}
                    />
                </div>
            </div>
            <div className="grid gap-10 py-5 grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-4">
                {accommodations.length === 0 && isLoading ? (
                    Array.from({ length: 8 }).map((_, index) => <SkeletonMainCard key={index} />)
                ) : accommodations.length > 0 ? (
                    accommodations.map((item, index) => (
                        <Cards key={`${item.ID}-${index}`} images={item.Images.map((img) => img.ImageUrl)} item={item} />
                    ))
                ) : (
                    <div className="col-span-full text-center text-gray-500">
                        <div className="relative">
                            <img
                                src="/noHouse.jpg"
                                alt="img"
                                className="w-full h-[50vh] object-cover object-bottom"
                            />
                            <div className="absolute inset-10 flex items-center justify-center">
                                <p className="text-black text-2xl font-bold">Нет данных</p>
                            </div>
                        </div>
                    </div>
                )}
            </div>
            {isMapVisible && (
                <div className="fixed top-0 right-0 w-full lg:w-[40vw] h-full z-50 bg-white border-l border-gray-300 shadow-lg"
                >
                    <div className="h-full relative">
                        <div className="flex justify-end p-4 absolute top-0 right-2 z-[1000]">
                            <button
                                onClick={() => dispatch(setMapVisible(false))}
                                className="text-gray-700 lg:text-lg hover:text-black font-semibold  bg-white px-3 py-1 rounded-xl"
                            >
                                ✕
                            </button>
                        </div>
                        <MapSearch listings={mapFiltered} onBoundsChange={setMapFiltered} />
                    </div>
                </div>
            )}
            <AppDownloadBanner/>
        </div>
    );
};

export default AccommodationList;
