import React, { useState } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/swiper-bundle.css';
import { Navigation } from 'swiper/modules';
import { useGetHandbookQuery } from "../../../../store/slices/handbook/handbookSlice";
import { IoIos<PERSON>rrowForward, IoIosArrowBack } from "react-icons/io";
import SkeletonLoader from "../../../../shared/ui/skeleton/categorySkeleton";

const CategoryFilter: React.FC<{ onSelectCategory: (categoryId: string | null) => void }> = ({ onSelectCategory, setSelectedCategoryId, selectedCategoryId }) => {
    const { data: categoryList, error, isLoading } = useGetHandbookQuery('dictionary/categories/get_all');

    const handleCategoryClick = (categoryId: string) => {
        setSelectedCategoryId(categoryId);
        onSelectCategory(categoryId);
    };

    if (isLoading) {
        return <SkeletonLoader />;
    }

    if (error) {
        return <div>Error loading categories</div>;
    }

    if (!categoryList || categoryList.length === 0) {
        return <div>No categories available</div>;
    }

    return (
        <div className="relative flex md:w-[700px] lg:w-[900px] md:px-10">
            <div className="absolute -left-5 top-1/2 transform -translate-y-1/2 z-70 hidden md:block ">
                <button className="prev-button py-1 px-3 bg-white border border-red-500 rounded-full text-red-500 hover:bg-red-100 cursor-pointer">
                    <IoIosArrowBack size={20} className="text-text-light-red" />
                </button>
            </div>
            <Swiper
                modules={[Navigation]}
                spaceBetween={10}
                slidesPerView={4}
                breakpoints={{
                    640: {
                        slidesPerView: 3.5,
                    },
                    768: {
                        slidesPerView: 5,
                    },
                    1024: {
                        slidesPerView: 7,
                    },
                }}
                navigation={{
                    prevEl: '.prev-button',
                    nextEl: '.next-button',
                }}
            >
                {categoryList.map((category) => (
                    <SwiperSlide key={category.Id}>
                            <div
                                className={`flex flex-col h-full items-center p-2 cursor-pointer transition-all duration-300 ${
                                    selectedCategoryId === category.Id ? 'border-b-4 border-border-bottom' : 'border-b-4 border-transparent hover:border-border-bottom'
                                }`}
                                onClick={() => handleCategoryClick(category.Id)}
                            >
                                <div
                                    className="h-10 w-10 bg-no-repeat bg-center bg-contain"
                                    style={{
                                        backgroundImage: `url('${category.Icon}')`
                                    }}
                                />
                                <span
                                    className="mt-2 text-sm md:text-md font-semibold text-gray-600 text-center">{category?.Name}</span>
                            </div>
                    </SwiperSlide>
                    ))}
            </Swiper>
            <div className="absolute -right-5 top-1/2 transform -translate-y-1/2 z-10 hidden md:block">
                <button className="next-button py-1 px-3 bg-white border border-red-500 rounded-full text-red-500 hover:bg-red-100 cursor-pointer">
                    <IoIosArrowForward size={20} className="text-text-light-red" />
                </button>
            </div>
        </div>
    );
};

export default CategoryFilter;
