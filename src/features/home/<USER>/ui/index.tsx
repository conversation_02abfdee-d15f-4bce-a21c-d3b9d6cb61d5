import React, { useState, useRef } from 'react';
import { IoIosArrowUp, IoMdClose } from 'react-icons/io';
import { useDispatch } from "react-redux";
import PriceRangeSlider from "../priceRangeSlider";
import SelectionRoomsAndBeds from "../selectionRoomsAndBeds";
import Facilities from "../facilities";
import RatingFilter from "../rating/RatingFilter";
import { useTranslation } from "react-i18next";
import { useOutsideClick } from "../../../../shared/hooks/useOutsideClick";

// Универсальный компонент для фильтров
const FilterSection = ({ title, isOpen, toggleSection, children, refProp }) => (
    <div className="relative" ref={refProp}>
        <button
            className="text-sm font-medium px-4 py-2 rounded-md flex items-center"
            onClick={toggleSection}
        >
            <span className="mr-2">{title}</span>
            <IoIosArrowUp className={`transition-transform duration-300 ${isOpen ? '-rotate-180' : ''}`} />
        </button>
        {isOpen && children}
    </div>
);

const FacilitiesFilter = ({ onApplyFilters, resetCategory  }) => {
    const { t } = useTranslation();

    const [openSection, setOpenSection] = useState<string | null>(null);
    const [selectedFacilities, setSelectedFacilities] = useState<number[]>([]);
    const [minPrice, setMinPrice] = useState<number>(200);
    const [maxPrice, setMaxPrice] = useState<number>(10000);
    const [roomsQuantity, setRoomsQuantity] = useState<number>(0);
    const [peopleQuantity, setPeopleQuantity] = useState<number>(0);
    const [selectedRating, setSelectedRating] = useState<number>(0);
    const [showFilters, setShowFilters] = useState(false); // Для мобильных устройств

    const bookingRef = useRef(null);
    const roomsRef = useRef(null);
    const facilitiesRef = useRef(null);
    const ratingRef = useRef(null);

    const applyFilters = () => {
        const newFilters = {
            selectedFacilities: selectedFacilities,
            minPrice: minPrice !== 200 ? minPrice : null,
            maxPrice: maxPrice !== 10000 ? maxPrice : null,
            roomsQuantity: roomsQuantity > 0 ? roomsQuantity : null,
            peopleQuantity: peopleQuantity > 0 ? peopleQuantity : null,
            selectedRating: selectedRating > 0 ? selectedRating : null,
        };
        onApplyFilters(newFilters);
        setOpenSection(null);
        setShowFilters(false);
    };

    const clearFilters = () => {
        const newFilters = {
            selectedFacilities: [],
            minPrice: null,
            maxPrice: null,
            roomsQuantity: null,
            peopleQuantity: null,
            selectedRating: null,
        };

        // Сравниваем новые фильтры с текущими
        const filtersChanged = JSON.stringify(newFilters) !== JSON.stringify({
            selectedFacilities,
            minPrice: minPrice !== 200 ? minPrice : null,
            maxPrice: maxPrice !== 10000 ? maxPrice : null,
            roomsQuantity: roomsQuantity > 0 ? roomsQuantity : null,
            peopleQuantity: peopleQuantity > 0 ? peopleQuantity : null,
            selectedRating: selectedRating > 0 ? selectedRating : null,
        });

        if (filtersChanged) {
            onApplyFilters(newFilters);
        }

        resetCategory();

        setSelectedFacilities([]);
        setMinPrice(200);
        setMaxPrice(10000);
        setRoomsQuantity(0);
        setPeopleQuantity(0);
        setSelectedRating(0);
        setOpenSection(null);
        setShowFilters(false);
    };

    const toggleSection = (section: string) => setOpenSection(openSection === section ? null : section);

    useOutsideClick(bookingRef, () => setOpenSection(null), openSection === 'booking');
    useOutsideClick(roomsRef, () => setOpenSection(null), openSection === 'rooms');
    useOutsideClick(facilitiesRef, () => setOpenSection(null), openSection === 'facilities');
    useOutsideClick(ratingRef, () => setOpenSection(null), openSection === 'rating');

    return (
        <div className="w-full border rounded-md p-2 shadow-md">
            {/* Фильтры для больших экранов */}
            <div className="hidden md:flex justify-between items-center">
                <div className="flex gap-5">
                    <FilterSection
                        title={t('filter.priceRange')}
                        isOpen={openSection === 'booking'}
                        toggleSection={() => toggleSection('booking')}
                        refProp={bookingRef}
                    >
                        <PriceRangeSlider
                            minPrice={minPrice}
                            setMinPrice={setMinPrice}
                            maxPrice={maxPrice}
                            setMaxPrice={setMaxPrice}
                        />
                    </FilterSection>

                    <FilterSection
                        title={t('filter.roomsAndBeds')}
                        isOpen={openSection === 'rooms'}
                        toggleSection={() => toggleSection('rooms')}
                        refProp={roomsRef}
                    >
                        <SelectionRoomsAndBeds
                            roomsQuantity={roomsQuantity}
                            setRoomsQuantity={setRoomsQuantity}
                            peopleQuantity={peopleQuantity}
                            setPeopleQuantity={setPeopleQuantity}
                        />
                    </FilterSection>

                    <FilterSection
                        title={t('filter.conveniences')}
                        isOpen={openSection === 'facilities'}
                        toggleSection={() => toggleSection('facilities')}
                        refProp={facilitiesRef}
                    >
                        <Facilities
                            updateSelectedFacilities={setSelectedFacilities}
                            selectedFacilities={selectedFacilities}
                        />
                    </FilterSection>

                    <FilterSection
                        title={t('filter.byRating')}
                        isOpen={openSection === 'rating'}
                        toggleSection={() => toggleSection('rating')}
                        refProp={ratingRef}
                    >
                        <RatingFilter
                            selectedRating={selectedRating}
                            setSelectedRating={setSelectedRating}
                        />
                    </FilterSection>
                </div>

                {/* Кнопки очистки и применения фильтров */}
                <div className="flex gap-3">
                    <button
                        onClick={clearFilters}
                        className="text-gray-500 hover:text-gray-700 text-sm"
                    >
                        {t('filter.clearAll')}
                    </button>
                    <button
                        onClick={applyFilters}
                        className="bg-black text-white px-3 py-1.5 rounded-md text-sm"
                    >
                        {t('filter.apply')}
                    </button>
                </div>
            </div>

            {/* Фильтры для мобильных устройств */}
            <div className="flex md:hidden justify-between items-center">
                <button
                    onClick={() => setShowFilters(!showFilters)}
                    className="flex items-center text-sm font-medium text-gray-600"
                >
                    <img src='/filter.svg' alt='img' />
                    <span className='ml-2'>{t('filter.filters')}</span>
                </button>
                <button
                    onClick={clearFilters}
                    className="text-gray-500 hover:text-gray-700 text-sm"
                >
                    {t('filter.clearAll')}
                </button>
            </div>

            {showFilters && (
                <div className="fixed z-10 inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                    <div className="absolute left-0 top-0 md:hidden h-full w-full bg-white px-5 py-5 overflow-y-scroll">
                        <h2 className="text-center font-semibold text-light-text text-xl mb-4">{t('filter.filters')}</h2>
                        <IoMdClose className="absolute right-5 top-5 cursor-pointer" size={30} onClick={() => setShowFilters(false)} />

                        <PriceRangeSlider
                            minPrice={minPrice}
                            setMinPrice={setMinPrice}
                            maxPrice={maxPrice}
                            setMaxPrice={setMaxPrice}
                        />
                        <SelectionRoomsAndBeds
                            roomsQuantity={roomsQuantity}
                            setRoomsQuantity={setRoomsQuantity}
                            peopleQuantity={peopleQuantity}
                            setPeopleQuantity={setPeopleQuantity}
                        />
                        <Facilities
                            updateSelectedFacilities={setSelectedFacilities}
                            selectedFacilities={selectedFacilities}
                        />
                        <RatingFilter
                            selectedRating={selectedRating}
                            setSelectedRating={setSelectedRating}
                        />

                        <div className="flex justify-center gap-3 mt-10">
                            <button onClick={clearFilters} className="text-gray-500 hover:text-gray-700 text-sm">
                                {t('filter.clearAll')}
                            </button>
                            <button onClick={applyFilters} className="bg-black text-white px-3 py-1.5 rounded-md text-sm">
                                {t('filter.apply')}
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default FacilitiesFilter;
