import React, { useState, useEffect, useRef } from 'react';
import { FaSearch, FaTimes } from 'react-icons/fa';
import { useDispatch } from 'react-redux';
import { setSearchTerm, clearAccommodations } from '../../../../store/slices/accommodation/accommodationSlice';
import {useNavigate} from "react-router-dom";

function SearchMap() {
    const dispatch = useDispatch();
    const navigate = useNavigate()
    const [searchTerm, setSearchTermLocal] = useState('');
    const debounceTimeoutRef = useRef(null);

    useEffect(() => {
        if (debounceTimeoutRef.current) {
            clearTimeout(debounceTimeoutRef.current);
        }

        debounceTimeoutRef.current = setTimeout(() => {
            dispatch(setSearchTerm(searchTerm));
        }, 500);

        return () => {
            if (debounceTimeoutRef.current) {
                clearTimeout(debounceTimeoutRef.current);
            }
        };
    }, [searchTerm, dispatch]);

    const handleSearchChange = (e) => {
        setSearchTermLocal(e.target.value);
    };

    const handleClearSearch = () => {
        setSearchTermLocal('');
        dispatch(setSearchTerm('')); // Сбрасываем поиск
    };

    return (
        <div className="flex items-center relative">
            <input
                type="text"
                placeholder="Поиск"
                value={searchTerm}
                onChange={handleSearchChange}
                className="w-full py-2 px-4 rounded-l-full bg-gray-100 outline-none border border-gray-300"
            />
            {searchTerm && (
                <button
                    className="absolute right-14 text-gray-500 hover:text-gray-700"
                    onClick={handleClearSearch}
                >
                    <FaTimes />
                </button>
            )}
            <button className="bg-black text-white px-4 py-3 rounded-full -ml-4">
                <FaSearch />
            </button>
        </div>
    );
}

export default SearchMap;
