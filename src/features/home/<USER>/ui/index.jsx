import React, { useState, useEffect, useRef } from 'react';
import { FaSearch, FaTimes } from 'react-icons/fa';
import { useDispatch } from 'react-redux';
import { setSearchTerm, clearAccommodations } from '../../../../store/slices/accommodation/accommodationSlice';
import {useNavigate} from "react-router-dom";
import {useTranslation} from "react-i18next";
import { TfiWorld } from "react-icons/tfi";
import {toggleMap} from "../../../../store/slices/uiSlice/uiSlice.js";

function Search() {
    const dispatch = useDispatch();
    const navigate = useNavigate()
    const [searchTerm, setSearchTermLocal] = useState('');
    const debounceTimeoutRef = useRef(null);
    const { t } = useTranslation();

    useEffect(() => {
        if (debounceTimeoutRef.current) {
            clearTimeout(debounceTimeoutRef.current);
        }
        debounceTimeoutRef.current = setTimeout(() => {
            dispatch(setSearchTerm(searchTerm.toLowerCase()));
        }, 500);

        return () => {
            if (debounceTimeoutRef.current) {
                clearTimeout(debounceTimeoutRef.current);
            }
        };
    }, [searchTerm, dispatch]);

    const handleSearchChange = (e) => {
        const newTerm = e.target.value;
        setSearchTermLocal(newTerm);

        if (window.location.pathname !== '/') {
            navigate('/');
        }
    };

    const handleClearSearch = () => {
        setSearchTermLocal('');
        dispatch(setSearchTerm(''));
    };

    return (
        <div className="flex items-center relative">
            <input
                type="text"
                placeholder={t('header.search')}
                value={searchTerm}
                onChange={handleSearchChange}
                className="w-full py-2 px-4 rounded-l-full bg-gray-100 outline-none border border-gray-300"
            />
            {searchTerm && (
                <button
                    className="absolute right-32 text-gray-500 hover:text-gray-700"
                    onClick={handleClearSearch}
                >
                    <FaTimes/>
                </button>
            )}
            <button className="bg-black text-white px-4 py-2 rounded-full -ml-4 flex gap-2 items-center"
                    onClick={() => {
                        if (window.location.pathname !== '/') {
                            navigate('/');
                        }
                        dispatch(toggleMap());
                    }}
            >
                <TfiWorld size={25}/>
                <span className="hidden md:block">Карта</span>
            </button>
        </div>
    );
}

export default Search;
