import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import {API_IMG} from "../../../../shared/config/api.js";
import Button from "../../../../shared/ui/button/Button.jsx";
import noImg from "/public/noImg.jpg"
import {useTranslation} from "react-i18next";

const AccommodationSelector = ({ selectedAccommodation, onSelect, myAccommodation }) => {
    const {t} = useTranslation()
    const [isActive, setIsActive] = useState(false);

    const handleSelect = (accommodation) => {
        onSelect(accommodation);
        setIsActive(false);
    };


    return (
        <div className='relative w-full mb-5 lg:mb-0'>
            <button
                className='flex items-center justify-between w-max p-2 border rounded-md font-semibold cursor-pointer gap-4'
                onClick={() => setIsActive(!isActive)}
            >
                <div className='flex items-center'>
                    {selectedAccommodation && (
                        <img
                            src={selectedAccommodation?.Accommodation?.Images.length > 0 ? API_IMG+selectedAccommodation?.Accommodation?.Images[0].ImageUrl : noImg}
                            alt='img'
                            className='w-10 h-10 rounded-md mr-2'
                        />
                    )}
                    {selectedAccommodation ? selectedAccommodation.Accommodation.Title : t('ownerPage.chooseAccommodation')}
                </div>
                <span>&#9660;</span>
            </button>
            {isActive && (
                <div className='absolute z-20 top-full mt-2 bg-white shadow-lg rounded-md border'>
                    <div className='p-4'>
                        {myAccommodation ? (
                            myAccommodation?.map((acc) => {
                                return (
                                    <div
                                        key={acc.Accommodation.ID}
                                        className='flex items-center justify-between p-2 cursor-pointer hover:bg-gray-100'
                                        onClick={() => handleSelect(acc)}
                                    >
                                        <div className='flex items-center'>
                                            <img
                                                src={acc.Accommodation.Images.length > 0 ? API_IMG+acc.Accommodation.Images[0].ImageUrl : noImg}
                                                alt={acc.Accommodation.Title}
                                                className='w-10 h-10 rounded-md mr-2'
                                            />
                                            <div>
                                                <p className='font-semibold'>{acc.Accommodation.Title}</p>
                                                <p className={`text-sm font-medium ${acc.Accommodation.IsAvailable ? 'text-green-500' : 'text-text-light-red'}`}>
                                                    {acc.Accommodation.IsAvailable ? 'активный' : 'отключен'}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                )
                            })
                        ): (
                            <p>Нет данных</p>
                        )}
                    </div>
                </div>
            )}
        </div>
    );
};

export default AccommodationSelector;
