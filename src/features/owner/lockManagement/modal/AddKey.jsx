import React, { useEffect, useState } from 'react';
import { useForm, FormProvider,useWatch } from 'react-hook-form';
import Modal from "shared/ui/modal/Modal.jsx";
import InputField from "shared/ui/inputField/InputField.jsx";
import SelectInput from "../../../../shared/ui/inputField/SelectInput.jsx";
import Spinner from "../../../../shared/ui/spinner/Spinner.jsx";
import {useTranslation} from "react-i18next";

function AddKeyModal({ isOpen, onClose, onSave, initialValues = {},loadingLockKeys }) {
    const {t} = useTranslation()
    const isValidDate = (date) => {
        const timestamp = typeof date === 'string' ? parseInt(date, 10) : date;
        return timestamp && !isNaN(new Date(timestamp).getTime());
    };

    const methods = useForm({
        defaultValues: {
            KeyboardPwdName: initialValues.KeyboardPwdName || '',
            PasscodeTypeId: initialValues.PasscodeTypeId || 2,
            StartDate: isValidDate(initialValues.StartDate) ? new Date(parseInt(initialValues.StartDate, 10)).toISOString().slice(0, 16) : '',
            EndDate: isValidDate(initialValues.EndDate) ? new Date(parseInt(initialValues.EndDate, 10)).toISOString().slice(0, 16) : '',
            KeyboardPwd: initialValues.KeyboardPwd || '',
        },
    });


    const handleSubmit = (data) => {
        const dataToSave = {
            ...data,
        };

        if (data.PasscodeTypeId === 2) {
            dataToSave.StartDate = new Date(data.StartDate).getTime().toString();
            dataToSave.EndDate = new Date(data.EndDate).getTime().toString();
        } else {
            delete dataToSave.StartDate;
            delete dataToSave.EndDate;
        }
        onSave(dataToSave);
    };


    useEffect(() => {
        if(!loadingLockKeys){
            methods.reset({
                KeyboardPwdName: initialValues.KeyboardPwdName || '',
                PasscodeTypeId: initialValues.PasscodeTypeId || 2,
                StartDate: isValidDate(initialValues.StartDate) ? new Date(parseInt(initialValues.StartDate, 10)).toISOString().slice(0, 16) : '',
                EndDate: isValidDate(initialValues.EndDate) ? new Date(parseInt(initialValues.EndDate, 10)).toISOString().slice(0, 16) : '',
                KeyboardPwd: initialValues.KeyboardPwd || '',
            });
        }
    }, [initialValues, methods]);


    return (
        <Modal
            isOpen={isOpen}
            onClose={onClose}
            title={initialValues.KeyboardPwdName ? t('addLock.editKey') : t('addLock.addNewKey')}
            style='w-full md:w-1/3'
            confirmText='Добавить'
            buttonHidde={false}
        >
            <FormProvider {...methods}>
                <form onSubmit={methods.handleSubmit(handleSubmit)}>
                    <SelectInput
                        label={t('addLock.accessType')}
                        name="PasscodeTypeId"
                        list={[{ id: 1, title: t('addLock.constant') }, { id: 2, title: t('addLock.temporary') }]}
                        mapFunction={(item) => ({ id: item.id, title: item.title })}
                        required
                    />
                    <InputField
                        label={t('addLock.keyName')}
                        name="KeyboardPwdName"
                        type="text"
                        placeholder={t('placeholder.enterKeyName')}
                        required
                    />
                    {methods.watch('PasscodeTypeId') === 2 && (
                        <>
                            <InputField
                                label={t('addLock.startDateAndTime')}
                                name="StartDate"
                                type="datetime-local"
                                placeholder={t('addLock.startDateAndTime')}
                                required
                            />
                            <InputField
                                label={t('addLock.endDateAndTime')}
                                name="EndDate"
                                type="datetime-local"
                                placeholder={t('addLock.endDateAndTime')}
                                required
                            />
                        </>
                    )}
                    <InputField
                        label={t('addLock.keyCode')}
                        name="KeyboardPwd"
                        type="text"
                        placeholder={t('placeholder.enterKeyCode')}
                        required
                    />
                    <div className="flex justify-center gap-5">
                        <button type="button" onClick={onClose} className="w-full border px-5 py-2 text-red-500 font-semibold rounded-md hover:bg-text-light-red hover:text-white">
                            {t('button.cancel')}
                        </button>
                        <button type="submit" disabled={loadingLockKeys} className={`w-full px-5 py-2 bg-btn-blue text-white font-semibold rounded-md ${loadingLockKeys ? 'cursor-not-allowed' : 'cursor-pointer'}`}>
                            {loadingLockKeys ? <p className="flex justify-center px-5"><Spinner /></p> : <span> {t('button.save')}</span>}
                        </button>
                    </div>
                </form>
            </FormProvider>

        </Modal>
    );
}

export default AddKeyModal;
