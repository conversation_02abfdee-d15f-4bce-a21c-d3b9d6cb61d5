import React from 'react';
import Modal from "shared/ui/modal/Modal.jsx";
import Spinner from "../../../../shared/ui/spinner/Spinner.jsx";

function DeleteKey({isOpen, onClose, keyName,onSave,keyId, loadingLockKeys }) {


    return (
        <Modal
            isOpen={isOpen}
            onClose={onClose}
            onConfirm={() => onSave(keyId)}
            title='Удаление ключа'
            confirmText={loadingLockKeys ? <Spinner /> : 'Подтвердить'}
            confirmButtonProps={{
                className: `w-full px-4 py-2 bg-blue-500 text-white rounded-md ${loadingLockKeys ? 'cursor-not-allowed' : 'cursor-pointer'}`,
                disabled: loadingLockKeys,
            }}
            style='w-1/3'


        >
            <div className='flex flex-col items-center justify-center my-5'>
                <p className='font-semibold'>Вы точно хотите удалить ключ?</p>
                <p className='font-semibold text-text-light-red text-xl'>{keyName}</p>
            </div>
        </Modal>
    );
}

export default DeleteKey;