import { RxCross1 } from "react-icons/rx";
import React, {useEffect, useState} from "react";
import {useDispatch, useSelector} from "react-redux";
import {getAllAccomodations} from "../../../store/actions/admin/adminListAction.js";
import {getTTLocks} from "../../../store/actions/lockKey/lockKey.js";
import Button from "../../../shared/ui/button/Button.jsx";

function KeyLockModal({
                   data,
                   isOpen,
                   onClose,
                   onConfirm,
                   title,
                   confirmText = 'Продолжить',
                   cancelText = 'Отмена',
                   style,
                   buttonHidde = true,
                   confirmButtonProps = {},
                   crossClose=false,
                   id=null
               }) {


    const [isActive, setIsActive] = useState(true);
    const [selectedLock, setSelectedLock] = useState(null);


    if (!isOpen) return null;


    const handleSelect = (items) => {
        setSelectedLock(items)
        setIsActive(false);
    };


    const handleBindLockSubmit = () => {
        if (selectedLock) {
            onConfirm(selectedLock);
            setSelectedLock(null)
        }
    };

    return (
        <div className="fixed z-50 inset-0 bg-black bg-opacity-50 flex items-center justify-center">
            <div className={`${style} bg-white mx-10 md:m-0 p-4 sm:p-6 rounded shadow-lg relative`}>
                {crossClose &&
                    <span className='absolute top-2 right-2 p-1 cursor-pointer hover:bg-gray-200 hover:rounded-full '
                          onClick={onClose}>
                        <RxCross1/>
                   </span>
                }
                <h2 className="text-lg sm:text-lg font-bold mb-2 sm:mb-4 text-center">{title}</h2>
                <div className='flex justify-center'>
                    <button
                        className='flex items-center justify-between w-max p-2 border rounded-md font-semibold cursor-pointer gap-4'
                        onClick={() => setIsActive(!isActive)}
                    >
                        <span>{selectedLock ? selectedLock?.LockAlias : 'Выберите ключ'}</span>
                        <span>&#9660;</span>
                    </button>
                </div>
                {isActive && (
                    <div className='z-20 top-full mt-2 bg-white rounded-md '>
                        <div className=' overflow-auto max-h-70'>
                            {data?.length > 0 ? (
                                data.map((items) => (
                                    <div
                                        key={items.LockId}
                                        className='flex items-center justify-between p-2 cursor-pointer hover:bg-gray-100'
                                        onClick={() => handleSelect(items)}
                                    >
                                        <div className='flex items-center'>
                                            <div>
                                                <p className='font-semibold'>{items.LockName}</p>
                                                <p>{items.LockAlias}</p>
                                            </div>
                                        </div>
                                    </div>
                                ))
                            ) : (
                                <p>Нет доступных замков</p>
                            )}
                        </div>
                    </div>
                )}
                {
                    selectedLock && (
                        <div className='flex justify-center mt-4'>
                            <Button
                                name={'Привязать'}
                                onClick={handleBindLockSubmit}
                                style={'bg-btn-blue text-white'}
                            />
                        </div>
                    )
                }
                {
                    buttonHidde && (
                        <div className="flex justify-center gap-5">
                            <button
                                className="w-full border px-5 py-2 text-red-500 hover:bg-text-light-red hover:text-white font-semibold rounded-md"
                                onClick={onClose}>{cancelText}</button>
                            <button
                                onClick={onConfirm}
                                {...confirmButtonProps}
                            >
                                {confirmText}
                            </button>
                        </div>
                    )
                }
            </div>
        </div>
    );
}

export default KeyLockModal;
