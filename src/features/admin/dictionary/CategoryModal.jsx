import { useForm, FormProvider } from "react-hook-form";
import { useEffect, useState } from "react";
import { RxCross1 } from "react-icons/rx";
import InputField from "../../../shared/ui/inputField/InputField.jsx";

function DictionaryModal({ isOpen, onClose, selected, title, onSave }) {
    const methods = useForm();
    const { handleSubmit, reset, setValue, watch } = methods;
    const [preview, setPreview] = useState(null);

    useEffect(() => {
        if (selected) {
            const base64Icon = selected?.Icon || "";
            reset({
                ru: selected?.Name?.ru || selected?.Value?.ru || "",
                ky: selected?.Name?.ky || selected?.Value?.ky || "",
                en: selected?.Name?.en || selected?.Value?.en || "",
                icon: base64Icon,
                facilityCategoryId: selected?.FacilityCategoryId || ""
            });
            setPreview(base64Icon);
        } else {
            reset({
                ru: "",
                ky: "",
                en: "",
                icon: "",
                facilityCategoryId: "",
            });
            setPreview(null);
        }
    }, [selected, reset]);

    useEffect(() => {
        if (!isOpen) {
            reset({
                ru: "",
                ky: "",
                en: "",
                icon: "",
            });
            setPreview(null);
        }
    }, [isOpen, reset]);

    const handleFileChange = (event) => {
        const file = event.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onloadend = () => {
                const base64String = reader.result;
                setValue("icon", base64String);
                setPreview(base64String);
            };
            reader.readAsDataURL(file);
        }
    };

    const onSubmit = (data) => {
        onSave(data);
        onClose();
    };

    if (!isOpen) return null;

    return (
        <div className="fixed z-50 inset-0 bg-black bg-opacity-50 flex items-center justify-center">
            <div className="bg-white mx-10 md:m-0 p-4 sm:p-6 rounded shadow-lg relative w-1/3">
                <span className="absolute top-2 right-2 p-1 cursor-pointer hover:bg-gray-200 hover:rounded-full" onClick={onClose}>
                    <RxCross1 />
                </span>
                <h1 className="font-semibold text-center text-xl mb-5">{title}</h1>

                <FormProvider {...methods}>
                    <form onSubmit={handleSubmit(onSubmit)}>
                        <InputField label="Русский" name="ru" type="text" placeholder="Введите" required />
                        <InputField label="Кыргызский" name="ky" type="text" placeholder="Введите" required />
                        <InputField label="Английский" name="en" type="text" placeholder="Введите" required />

                        {/* Блок загрузки иконки */}
                        <div className="flex flex-col gap-2">
                            <label className="font-bold">Иконка</label>
                            {preview && <img src={preview} alt="Preview" className="w-16 h-16 object-cover rounded-md" />}
                            <input type="file" accept="image/*" onChange={handleFileChange} />
                        </div>

                        <button type="submit" className="mt-4 bg-blue-500 text-white px-4 py-2 rounded">
                            Сохранить
                        </button>
                    </form>
                </FormProvider>
            </div>
        </div>
    );
}

export default DictionaryModal;
