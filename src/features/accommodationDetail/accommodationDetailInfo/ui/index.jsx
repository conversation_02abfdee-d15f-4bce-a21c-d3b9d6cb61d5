import React, { useState } from "react";
import { FaStar } from "react-icons/fa";
import UserCard from "../../../../entities/userCard/ui/index.jsx";
import Modal from "../../../../shared/ui/modal/Modal.jsx";
import { useTranslation } from "react-i18next";
import Button from "../../../../shared/ui/button/Button.jsx";

const AccommodationDetailInfo = ({ room, owner, ownerID }) => {
    const { t, i18n  } = useTranslation();
    const [showAllFacilitiesModal, setShowAllFacilitiesModal] = useState(false);
    const [showAllRulesModal, setShowAllRulesModal] = useState(false);

    const handleShowAllFacilities = () => {
        setShowAllFacilitiesModal(true);
    };

    const handleShowAllRules = () => {
        setShowAllRulesModal(true);
    };

    const closeModal = () => {
        setShowAllFacilitiesModal(false);
        setShowAllRulesModal(false);
    };

    console.log(room, 'room')



    const getLocalizedValue = (item) => {
        switch (i18n.language) {
            case "ru":
                return item.ValueRu;
            case "en":
                return item.ValueEn;
            case "ky":
                return item.ValueKg;
            default:
                return item.ValueRu; // По умолчанию русский язык
        }
    };

    return (
        <>
            <div className="flex flex-col space-y-5 mt-5 md:mt-0">
                <div className='space-y-2'>
                    <div className='flex justify-between '>
                        <h2 className="text-xl md:text-3xl font-bold text-light-text">{room.Title}</h2>
                        <p className="flex gap-3 items-center border border-text-light-red rounded-3xl px-3 cursor-pointer h-[40px]">
                            <span className='text-yellow-400'><FaStar/></span>
                            <span className="text-black text-md font-semibold">{room.Rating}</span>
                        </p>
                    </div>
                    <p className='flex gap-1 items-center'>
                        <img src='/location.svg' alt='img' className='w-4 h-4'/>
                        <span>{room.LocationLabel}</span>
                    </p>
                    <div className='flex gap-2'>
                        <div className="flex gap-2 items-center text-gray-700">
                            <p className='bg-back-icon rounded-full p-2'>
                                <img src='/room.svg' alt='img' className='w-5 h-5'/>
                            </p>
                            <span>{t('detailPage.rooms')}:</span>
                            <span>{room.RoomsQuantity}</span>
                        </div>
                        <div className="flex gap-2 items-center text-gray-700">
                            <p className='bg-back-icon rounded-full p-2'>
                                <img src='/bed.svg' alt='img' className='w-5 h-5'/>
                            </p>
                            <span>{t('detailPage.beds')}:</span>
                            <span>{room.PeopleQuantity}</span>
                        </div>
                    </div>
                </div>

                <p className="flex flex-col w-full space-y-2">
                    <span className="text-light-text font-semibold">{t('detailPage.description')}:</span>
                    <span className="text-light-text">{room.Description}</span>
                </p>

                {room.Facilities && (
                    <div className="mb-4 pt-4 border-t">
                        <p className='text-blue-text-lock font-semibold mb-2 text-lg'>{t('detailPage.facilitiesTile')}</p>
                        <div className="w-max grid grid-cols-1 md:grid-cols-2 gap-4">
                            {room.Facilities.slice(0, 5).map((item, index) => (
                                <div key={index} className="flex items-center gap-2">
                                    {item.Icon && (
                                        <img
                                            src={item.Icon}
                                            alt={`icon-${index}`}
                                            className="w-7 h-7"
                                        />
                                    )}
                                    <p className="text-md font-semibold w-[200px] xl:w-[300px]">{getLocalizedValue(item)}</p>
                                </div>
                            ))}
                        </div>
                        {room.Facilities.length > 5 && (
                            <button
                                onClick={handleShowAllFacilities}
                                className=" border border-black px-2 py-1 rounded-md mt-2"
                            >
                                {`${t('detailPage.showAll')} ${room.Facilities.length}`}
                            </button>
                        )}
                    </div>
                )}

                {room.Rules && (
                    <div className="mb-4 py-4 w-full border-y">
                        <p className='text-blue-text-lock font-semibold mb-2 text-lg'>{t('bookingBar.note')}</p>
                        <div className="w-max grid grid-cols-1 md:grid-cols-2 gap-4">
                            {room.Rules.slice(0, 5).map((item, index) => (
                                <div key={index} className="flex items-center gap-2">
                                    {item.Icon && (
                                        <img
                                            src={item.Icon}
                                            alt={`icon-${index}`}
                                            className="w-6 h-6"
                                        />
                                    )}
                                    <p className="text-red-600 font-semibold">{getLocalizedValue(item)}</p>
                                </div>
                            ))}
                        </div>
                        {room.Rules.length > 5 && (
                            <button
                                onClick={handleShowAllRules}
                                className="border border-black px-2 py-1 rounded-md mt-2"
                            >
                                {`${t('detailPage.showAll')} ${room.Rules.length}`}
                            </button>
                        )}
                    </div>
                )}

                <UserCard
                    avatarClassName='w-20 h-20'
                    title={t('role.owner')}
                    item={owner}
                    id={ownerID}
                />

                {/* Модальное окно для всех удобств */}

            </div>
            <Modal
                isOpen={showAllFacilitiesModal}
                onClose={closeModal}
                title={t('detailPage.facilitiesTile')}
                style="max-w-2xl mt-0"
                buttonHidde={false}
                crossClose={true}
            >
                <>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {room.Facilities.map((item, index) => (
                            <div key={index} className="flex items-center gap-2">
                                {item.Icon && (
                                    <img
                                        src={item.Icon}
                                        alt={`icon-${index}`}
                                        className="w-7 h-7"
                                    />
                                )}
                                <p className="text-md font-semibold">{getLocalizedValue(item)}</p>
                            </div>
                        ))}
                    </div>
                </>
            </Modal>

            <Modal
                isOpen={showAllRulesModal}
                onClose={closeModal}
                title={t('bookingBar.note')}
                style="max-w-2xl"
                buttonHidde={false}
                crossClose={true}
            >
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {room.Rules.map((item, index) => (
                        <div key={index} className="flex items-center gap-2">
                            {item.Icon && (
                                <img
                                    src={item.Icon}
                                    alt={`icon-${index}`}
                                    className="w-6 h-6"
                                />
                            )}
                            <p className="text-red-600 font-semibold">{getLocalizedValue(item)}</p>
                        </div>
                    ))}
                </div>
            </Modal>
        </>
    );
};

export default AccommodationDetailInfo;
