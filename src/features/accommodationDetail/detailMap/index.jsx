import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css'; // Не забудь импортировать стили для корректного отображения карты

// Импорт иконки маркера с помощью import
import markerIconPng from 'leaflet/dist/images/marker-icon.png';
import markerIcon2xPng from 'leaflet/dist/images/marker-icon-2x.png';
import markerShadowPng from 'leaflet/dist/images/marker-shadow.png';
import {API_IMG} from "../../../shared/config/api.js";

// Иконка маркера (с помощью import вместо require)
const customIcon = new L.Icon({
    iconUrl: markerIconPng,
    iconRetinaUrl: markerIcon2xPng,
    shadowUrl: markerShadowPng,
    iconSize: [25, 41],
    iconAnchor: [12, 41],
    popupAnchor: [1, -34],
    shadowSize: [41, 41]
});

function DetailMap({ location }) {
    const { Latitude, Longitude, LocationLabel } = location || {};

    // Проверяем, что координаты существуют
    if (!Latitude || !Longitude) {
        return <p>Координаты не найдены</p>;
    }

    // Преобразуем строки в числа
    const position = [parseFloat(Latitude), parseFloat(Longitude)];

    return (
        <div>
            <MapContainer
                className="h-96 w-full my-2 z-0"
                center={position}
                zoom={13}
            >
                <TileLayer
                    attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                    url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                />
                <Marker position={position} icon={customIcon}>
                    <Popup>
                        <div className='w-[100px] md:w-[200px]'>
                            <img
                                src={location.Images.length > 0 ? API_IMG+location?.Images[0]?.ImageUrl : '/noImg.jpg'}
                                alt='img'
                                className='rounded-md w-full h-[150px] object-cover'
                            />
                            <h2 className='my-2 font-semibold'>{location.Title}</h2>
                            <span className='m-0'>{LocationLabel}</span>
                        </div>
                    </Popup>
                </Marker>
            </MapContainer>
        </div>
    );
}

export default DetailMap;
