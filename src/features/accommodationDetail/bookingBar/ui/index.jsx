import React, { useState, useMemo, useEffect } from "react";
import { useDispatch, useSelector } from 'react-redux';
import { createNewBooking } from "../../../../store/actions/booking/Booking.js";
import {Link, useNavigate} from "react-router-dom";
import { fetchProfile } from "store/actions/auth/Auth.js";
import Button from "../../../../shared/ui/button/Button.jsx";
import DatePicker from "../../../../shared/ui/datePicker/index.jsx";
import Modal from "../../../../shared/ui/modal/Modal.jsx";
import {useTranslation} from "react-i18next";
import { formatDateRange} from "../../../../shared/utils/formatDate/index.js";

const BookingBar = ({ room, calendar }) => {
    const [checkInDate, setCheckInDate] = useState(null);
    const [checkOutDate, setCheckOutDate] = useState(null);
    const [guestCount, setGuestCount] = useState(1);
    const [childrenCount, setChildrenCount] = useState(0);
    const [dateError, setDateError] = useState("");
    const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
    const [isProfileModalOpen, setIsProfileModalOpen] = useState(false);
    const user = useSelector((state) => state.auth.user?.user);

    const dispatch = useDispatch();
    const navigate = useNavigate();
    const {t} = useTranslation()
    const { status, error, loading } = useSelector((state) => state.booking);
    const profile = useSelector((state) => state.auth.user?.profile);
    const isAuthenticated= useSelector(state => state.auth.isAuthenticated)



    const totalNights = useMemo(() => {
        if (checkInDate && checkOutDate) {
            const checkIn = new Date(checkInDate);
            const checkOut = new Date(checkOutDate);
            const diffTime = Math.abs(checkOut - checkIn);
            return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        }
        return 0;
    }, [checkInDate, checkOutDate]);

    const totalCost = useMemo(() => totalNights * room.Price, [totalNights, room.DiscountPrice]);

    useEffect(() => {
        if (!profile) {
            dispatch(fetchProfile());
        }
    }, [dispatch, profile]);

    const handleBooking = () => {
        if (!isAuthenticated) {
            setIsAuthModalOpen(true);
            return;
        }

        if(!user.Profile?.ISBookable) {
            setIsProfileModalOpen(true);
            return;
        }

        if (!checkInDate || !checkOutDate) {
            setDateError("Пожалуйста, выберите дату заезда и выезда.");
            return;
        }

        const formattedCheckInDate = formatDateRange(checkInDate);
        const formattedCheckOutDate = formatDateRange(checkOutDate);


        navigate(`/reservation/${room.ID}`, {
            state: {
                room,
                checkInDate: formattedCheckInDate,
                checkOutDate: formattedCheckOutDate,
                guestCount,
                childrenCount
            }
        });
    };

    const handleStartDateChange = (date) => {
        setCheckInDate(date);
        if (dateError) setDateError(''); // Clear the error when date is selected
    };

    const handleEndDateChange = (date) => {
        setCheckOutDate(date);
        if (dateError) setDateError(''); // Clear the error when date is selected
    };

    const handleAuthModalClose = () => {
        setIsAuthModalOpen(false);
    };

    const handleAuthModalConfirm = () => {
        navigate("/auth/login");
    };

    const handleProfileModalClose = () => {
        setIsProfileModalOpen(false);
    };

    const handleProfileModalConfirm = () => {
        navigate("/profile/personal-info"); // Перенаправление на заполнение паспорта
    };

    return (
        <div className='border p-5 shadow-md rounded-2xl lg:sticky top-5'>
            <div className='flex mb-4 items-center gap-3'>
                <h2 className="text-xl text-text-medium font-semibold ">{room.Price} {t('common.currencySom')}</h2>
                <span className='text-text-medium '>{t('bookingBar.perDay')}</span>
            </div>
            <DatePicker
                checkInDate={checkInDate}
                checkOutDate={checkOutDate}
                onStartDateChange={handleStartDateChange}
                onEndDateChange={handleEndDateChange}
                calendar={calendar}
                t={t}
                room={room}
            />
            {dateError && (
                <p className="text-sm text-red-600 font-semibold mb-1">{dateError}</p>
            )}
            <div className='flex flex-col lg:flex-row justify-between gap-2'>
                <div className="mb-4 w-full">
                    <label className="block text-gray-700 font-semibold">{t('bookingBar.numberGuests')}</label>
                    <select
                        className="w-full border rounded px-2 py-[5.5px] cursor-pointer"
                        value={guestCount}
                        onChange={(e) => setGuestCount(e.target.value)}
                    >
                        {Array.from({length: room.PeopleQuantity}, (_, i) => (
                            <option key={i + 1} value={i + 1}>
                                {i + 1}
                            </option>
                        ))}
                    </select>
                </div>
                <div className="mb-4 w-full">
                    <label className="block text-gray-700 font-semibold">{t('bookingBar.numberСhildren')}</label>
                    <input
                        type="number"
                        min="0"
                        value={childrenCount}
                        onChange={(e) => setChildrenCount(e.target.value)}
                        className="w-full border rounded px-2 py-1"
                    />
                </div>
            </div>

            {checkInDate && checkOutDate && (
                <div className="flex flex-col my-4 gap-3 border-t">
                    <div className='flex justify-between pt-4'>
                        <p className='font-semibold'>
                            {room.Price} {t('common.currencySom')} х {totalNights} {totalNights > 1 ? t('common.days') : t('common.day')}
                        </p>
                        <p className='font-semibold'>
                            {totalCost} {t('common.currencySom')}
                        </p>
                    </div>
                    <div className='flex justify-between'>
                        <p className='font-semibold'>
                        {t('common.total')}:
                        </p>
                        <p className='font-semibold'>
                            {totalCost} {t('common.currencySom')}
                        </p>
                    </div>
                </div>
            )}

            <Button
                style='bg-btn-bg-red text-white w-full py-3'
                name={t('button.order')}
                onClick={handleBooking}
            />
            <Modal
                isOpen={isAuthModalOpen}
                onClose={handleAuthModalClose}
                onConfirm={handleAuthModalConfirm}
                title={t('modal.mustLogin')}
                confirmText={t('auth.login')}
                cancelText={t('button.cancel')}
                confirmButtonProps={{
                    className: `w-full px-4 py-2 bg-blue-500 text-white rounded-md cursor-pointer`,
                }}
            >
                <div className='flex flex-col items-center justify-center my-5'>
                    <p className='font-medium'>
                        {t('common.please')},
                        <Link to='/auth/register'
                              className='text-blue-400'> {t('common.register')}
                        </Link> {t('common.or')}
                        <Link to='/auth/login'  className='text-blue-400'> {t('common.singIn')}
                        </Link>,  {t('modal.toProceedWithBooking')}.</p>
                </div>
            </Modal>

            <Modal
                isOpen={isProfileModalOpen}
                onClose={handleProfileModalClose}
                onConfirm={handleProfileModalConfirm}
                title={t('modal.verificationTitle')}
                confirmText={t('modal.verificationButton')}
                cancelText={t('button.cancel')}
                confirmButtonProps={{
                    className: `w-full px-4 py-2 bg-blue-500 text-white rounded-md cursor-pointer`,
                }}
            >
                <div className='flex flex-col items-center justify-center my-5'>
                    <p className='font-medium'>
                        {t("modal.verificationMessage")}
                    </p>
                </div>
            </Modal>
        </div>
    );
};

export default BookingBar;
