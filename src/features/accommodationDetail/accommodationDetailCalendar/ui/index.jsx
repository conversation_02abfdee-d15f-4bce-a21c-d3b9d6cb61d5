import { DateRange  } from 'react-date-range';
import 'react-date-range/dist/styles.css';
import 'react-date-range/dist/theme/default.css';
import {useEffect, useState} from "react";
import Modal from "../../../../shared/ui/modal/Modal.jsx"; // theme css file

const RoomCalendar = ({ role, bookings }) => {
    const [selectedBooking, setSelectedBooking] = useState(null);
    const [monthsToShow, setMonthsToShow] = useState(1);


    const bookedRanges = bookings.map(booking => ({
        startDate: new Date(booking.startDate),
        endDate: new Date(booking.endDate),
        key: 'selection',
        color: '#3d91ff'
    }));



    const handleDateClick = (ranges) => {
        const { selection } = ranges;
        const date = selection.startDate;
        const booking = bookings.find(booking => {
            const bookingStartDate = new Date(booking.startDate);
            const bookingEndDate = new Date(booking.endDate);
            return date >= bookingStartDate && date <= bookingEndDate;
        });
        if (booking) {
            setSelectedBooking(booking);
        }
    };

    const handleCloseModal = () => {
        setSelectedBooking(null);
    };


    useEffect(() => {
        const updateMonthsToShow = () => {
            if (window.innerWidth >= 768) {
                setMonthsToShow(1);
            } else {
                setMonthsToShow(1);
            }
        };

        window.addEventListener('resize', updateMonthsToShow);
        updateMonthsToShow(); // Вызов при монтировании компонента

        return () => window.removeEventListener('resize', updateMonthsToShow);
    }, []);


    return (
        <div className='p-5 shadow-xl w-max rounded-md'>
            <DateRange
                ranges={bookedRanges}
                onChange={handleDateClick}
                editableDateInputs={false}
                showDateDisplay={false}
                rangeColors={['#3d91ff']}
                moveRangeOnFirstSelection={false}
                months={monthsToShow}
                direction="horizontal"
            />
            {selectedBooking && (
                <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
                    <div className="bg-white rounded-lg shadow-lg p-6 w-11/12 md:w-1/2 lg:w-1/3">
                        <div className="flex justify-between items-center border-b pb-2">
                            <h2 className="md:text-xl font-semibold">Детальный просмотр бронирования</h2>
                        </div>
                        <div className="flex flex-col gap-2 mt-4">
                            <p><strong>Клиент:</strong> {selectedBooking.details.client}</p>
                            <p><strong>Количество людей:</strong> {selectedBooking.details.peopleCount}</p>
                            <p><strong>Дата начала:</strong> {selectedBooking.startDate}</p>
                            <p><strong>Дата окончания:</strong> {selectedBooking.endDate}</p>
                        </div>
                        <div className="flex justify-end mt-6">
                            <button
                                onClick={handleCloseModal}
                                className="bg-blue-500 hover:bg-blue-700 text-white py-1 px-4 rounded"
                            >
                                Закрыть
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default RoomCalendar;
