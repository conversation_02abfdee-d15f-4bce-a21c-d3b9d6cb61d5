import React, { useState, useRef } from "react";
import { API_IMG } from "../../../../shared/config/api.js";
import noImg from "/public/noImg.jpg";
import {useOutsideClick} from "../../../../shared/hooks/useOutsideClick.js";

const PhotoGallery = ({ photos = [], title }) => {
    const [currentSlide, setCurrentSlide] = useState(0);
    const [showModal, setShowModal] = useState(false);

    const modalRef = useRef(null);
    // Используем хук useOutsideClick для закрытия модального окна при клике вне его
    useOutsideClick(modalRef, () => setShowModal(false), showModal);

    const openModalWithSlide = (e, index) => {
        e.stopPropagation(); // предотвращаем закрытие модального окна
        setCurrentSlide(index); // Устанавливаем индекс выбранного фото
        setShowModal(true); // Открываем модальное окно
    };

    const handlePrev = () => {
        setCurrentSlide((prev) => (prev === 0 ? photos.length - 1 : prev - 1));
    };

    const handleNext = () => {
        setCurrentSlide((prev) => (prev === photos.length - 1 ? 0 : prev + 1));
    };

    // Заполняем массив до 5 фотографий, если их меньше
    const filledPhotos = [...photos];
    while (filledPhotos.length < 5) {
        filledPhotos.push({ ImageUrl: noImg });
    }

    return (
        <>
            <div className="relative hidden lg:grid md:grid-cols-4 md:grid-rows-2 gap-2.5 mb-4 max-h-[500px] overflow-hidden cursor-pointer"
            >
                <div className="col-span-1 row-span-1 ">
                    <img
                        src={filledPhotos[1].ImageUrl !== noImg ? `${API_IMG}${filledPhotos[1].ImageUrl}` : noImg}
                        alt={`${title} photo 2`}
                        className="w-full h-full object-cover rounded-lg shadow-lg bg-gray-200 "
                        onClick={(e) => openModalWithSlide(e,1)}
                    />
                </div>
                <div className="col-span-2 row-span-2 ">
                    <img
                        src={photos.length > 0 ? `${API_IMG}${photos[0].ImageUrl}` : noImg}
                        alt={`${title} main photo`}
                        className="w-full h-full object-cover  rounded-lg shadow-lg bg-gray-200"
                        onClick={(e) => openModalWithSlide(e,0)}
                    />
                </div>
                <div className="col-span-1 row-span-1">
                    <img
                        src={filledPhotos[2].ImageUrl !== noImg ? `${API_IMG}${filledPhotos[2].ImageUrl}` : noImg}
                        alt={`${title} photo 3`}
                        className="w-full h-full object-cover  rounded-lg shadow-lg bg-gray-200"
                        onClick={(e) => openModalWithSlide(e,2)}
                    />
                </div>
                <div className="col-span-1 row-span-1">
                    <img
                        src={filledPhotos[3].ImageUrl !== noImg ? `${API_IMG}${filledPhotos[3].ImageUrl}` : noImg}
                        alt={`${title} photo 4`}
                        className="w-full h-full object-cover rounded-lg shadow-lg bg-gray-200"
                        onClick={(e) => openModalWithSlide(e,3)}
                    />
                </div>
                <div className="col-span-1 row-span-1 relative">
                    <img
                        src={filledPhotos[4].ImageUrl !== noImg ? `${API_IMG}${filledPhotos[4].ImageUrl}` : noImg}
                        alt={`${title} photo 5`}
                        className="w-full h-full object-cover rounded-lg shadow-lg cursor-pointer bg-gray-200"

                    />
                    {photos.length > 5 && (
                        <button
                            className="absolute inset-0 bg-gray-800 bg-opacity-50 flex items-center justify-center text-white font-semibold text-2xl rounded-lg"
                            onClick={(e) => openModalWithSlide(e,4)}
                        >
                            +{photos.length - 5}
                        </button>
                    )}
                </div>
            </div>

            <div className="block lg:hidden">
                <div className="relative w-full h-full rounded-lg" id="gallery">
                    <div className="relative h-[300px] sm:h-80">
                        {photos.length > 0 ? (
                            photos.map((photo, index) => (
                                <div key={index}
                                     className={`absolute w-full ${index === currentSlide ? 'block' : 'hidden'}`}
                                     data-carousel-item={index === currentSlide}>
                                    <img src={photo.ImageUrl ? `${API_IMG}${photo.ImageUrl}` : noImg}
                                         className="block w-full h-[300px] object-cover rounded-2xl"
                                         alt={`${title} slide ${index}`}/>
                                </div>
                            ))
                        ) : (
                            <img src={noImg}
                                 className="block w-full h-[300px] object-cover rounded-2xl"
                                 alt='img'/>
                        )}
                    </div>
                    <button type="button"
                            className="absolute top-0 left-0 z-30 flex items-center justify-center h-full px-4 cursor-pointer group focus:outline-none"
                            onClick={handlePrev}>
                        <span
                            className="inline-flex items-center justify-center w-10 h-10 rounded-full bg-white/30 group-hover:bg-white/50 focus:ring-4 focus:ring-white">
                            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7"/>
                            </svg>
                            <span className="sr-only">Previous</span>
                        </span>
                    </button>
                    <button type="button"
                            className="absolute top-0 right-0 z-30 flex items-center justify-center h-full px-4 cursor-pointer group focus:outline-none"
                            onClick={handleNext}>
                        <span
                            className="inline-flex items-center justify-center w-10 h-10 rounded-full bg-white/30 group-hover:bg-white/50 focus:ring-4 focus:ring-white">
                            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"/>
                            </svg>
                            <span className="sr-only">Next</span>
                        </span>
                    </button>
                </div>
            </div>

            {/* Модальное окно для просмотра фотографий со слайдами */}
            {showModal && (
                <div className="fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center z-50">
                    <div ref={modalRef} className="sm:mx-10 xl:mx-0 bg-white rounded-lg shadow-lg p-4 w-max max-h-[90vh] overflow-y-hidden relative">
                        {/*<button*/}
                        {/*    onClick={() => setShowModal(false)}*/}
                        {/*    className="absolute top-2 right-2 text-black bg-amber-400 rounded px-4 py-2"*/}
                        {/*>*/}
                        {/*    Закрыть*/}
                        {/*</button>*/}
                        <div className="relative flex justify-center items-center">
                            <button
                                className="absolute left-0 z-30 flex items-center justify-center h-full px-4 cursor-pointer group focus:outline-none"
                                onClick={handlePrev}
                            >
                                <span className="inline-flex items-center justify-center w-10 h-10 rounded-full bg-white/30 group-hover:bg-white/50 focus:ring-4 focus:ring-white">
                                    <svg className="w-6 h-6 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7"/>
                                    </svg>
                                </span>
                            </button>
                            <img
                                src={photos[currentSlide]?.ImageUrl ? `${API_IMG}${photos[currentSlide].ImageUrl}` : noImg}
                                alt={`${title} photo ${currentSlide + 1}`}
                                className="w-[400px] md:w-[600px] max-h-[87vh] object-cover rounded-lg shadow-lg"
                            />
                            <button
                                className="absolute right-0 z-30 flex items-center justify-center h-full px-4 cursor-pointer group focus:outline-none"
                                onClick={handleNext}
                            >
                                <span className="inline-flex items-center justify-center w-10 h-10 rounded-full bg-white/30 group-hover:bg-white/50 focus:ring-4 focus:ring-white">
                                    <svg className="w-6 h-6 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"/>
                                    </svg>
                                </span>
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
};

export default PhotoGallery;
