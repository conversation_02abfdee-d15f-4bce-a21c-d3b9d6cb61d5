import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {resendVerificationCode, verifyEmail} from "../../../../store/actions/auth/Auth.js";

const useVerifyEmailForm = () => {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const email = useSelector(state => state.auth.email);

    // State for countdown timer
    const [timer, setTimer] = useState(60);
    const [isTimerActive, setIsTimerActive] = useState(true);

    // Effect for countdown timer
    useEffect(() => {
        if (isTimerActive && timer > 0) {
            const interval = setInterval(() => {
                setTimer(timer => timer - 1);
            }, 1000);
            return () => clearInterval(interval);
        } else {
            setIsTimerActive(false);
        }
    }, [isTimerActive, timer]);

    // Initialize timer on component mount
    const onSubmitCode = (data) => {
        dispatch(verifyEmail({ values: data.code, navigate }));
    };

    const onResendCode = () => {
        dispatch(resendVerificationCode(email));
        setIsTimerActive(true);
        setTimer(30);
    };

    return {
        onSubmitCode,
        onResendCode,
        isTimerActive,
        timer
    };
};

export default useVerifyEmailForm;
