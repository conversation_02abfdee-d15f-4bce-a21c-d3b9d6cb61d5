import { FormProvider, useForm } from "react-hook-form";
import Button from "shared/ui/button/Button";
import inputData from "../model/inputData";
import useVerifyEmailForm from "../model/useVerifyEmailForm";

const VerifyEmailForm = () => {
    const methods = useForm();
    const { handleSubmit } = methods;
    const { onResendCode, onSubmitCode, timer, isTimerActive } = useVerifyEmailForm();
    return <FormProvider {...methods}>
        <form onSubmit={handleSubmit(onSubmitCode)} className='flex flex-col justify-center mt-5 gap-2' noValidate>
            {inputData.code}
            {isTimerActive ? (
                <div className='flex justify-end'>
                    <span className='py-2 px-2 '>Отправить код через {timer} c</span>
                </div>
            ) : (
                <div className='flex justify-end'>
                    <Button type='button' style='bg-code-bg py-2' name='Отправить код' onClick={onResendCode} />
                </div>
            )}
            <div className='flex justify-center mt-5 sm:mt-1'>
                <Button name='Продолжить' type="submit" style='py-2 bg-blue-500 text-white hover:bg-blue-400' />
            </div>
        </form>
    </FormProvider>
}

export default VerifyEmailForm;