import { FormProvider, useForm } from "react-hook-form";
import Button from "shared/ui/button/Button";
import inputData from "../model/inputData";
import useForgotPasswordCodeForm from "../model/useForgotPasswordCodeForm";
import {useTranslation} from "react-i18next";

const ForgotPasswordCodeForm = () => {
    const methods = useForm();
    const { handleSubmit } = methods;
    const { onSubmit } = useForgotPasswordCodeForm();
    const {t} = useTranslation();
    const input = inputData(t)

    return <FormProvider {...methods}>
        <form onSubmit={handleSubmit(onSubmit)} className='flex flex-col justify-center mt-5'
            noValidate>
            {input.code}
            <div className='flex justify-center mt-1 '>
                <Button name={t('button.sendCode')} type="submit"
                    style='py-2 bg-blue-500 text-white hover:bg-blue-400' />
            </div>
        </form>
    </FormProvider>
}

export default ForgotPasswordCodeForm;
