import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { sendForgotPasswordCode } from "store/actions/auth/Auth";

const useForgotPasswordCodeForm = () => {
    const dispatch = useDispatch()
    const navigate = useNavigate()

    const onSubmit = async (data) => {
        try {
            const result = await dispatch(sendForgotPasswordCode(data.code));
            if (sendForgotPasswordCode.fulfilled.match(result)) {
                navigate('/auth/forgot-password/new-password')
            }
        } catch (e) {
            console.log('Error occurred:', e);
        }
    };

    return { onSubmit };
}

export default useForgotPasswordCodeForm;