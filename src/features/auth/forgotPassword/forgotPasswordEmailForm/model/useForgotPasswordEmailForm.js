import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { sendForgotPasswordEmail } from "store/actions/auth/Auth";
import { verifyCode } from "store/slices/auth/AuthSlice";

const useForgotPasswordEmailForm = () => {
    const dispatch = useDispatch()
    const navigate = useNavigate()
    const onSubmit = async (data) => {
        dispatch(verifyCode(data.Email))
        try {
            const result = await dispatch(sendForgotPasswordEmail(data.Email));
            if (sendForgotPasswordEmail.fulfilled.match(result)) {
                navigate('/auth/forgot-password/verify-forgot-password')
            }
        } catch (e) {
            console.log('Error occurred:', e);
        }
    };

    return { onSubmit };
}

export default useForgotPasswordEmailForm;