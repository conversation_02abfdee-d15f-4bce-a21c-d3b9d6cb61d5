import { FormProvider, useForm } from "react-hook-form";
import Button from "shared/ui/button/Button";
import inputData from "../model/inputData.jsx";
import useForgotPasswordEmailForm from "../model/useForgotPasswordEmailForm";
import {useTranslation} from "react-i18next";

const ForgotPasswordEmailForm = () => {
    const methods = useForm();
    const { handleSubmit } = methods;
    const { onSubmit } = useForgotPasswordEmailForm();
    const {t} = useTranslation()
    const input = inputData(t);

    return <FormProvider {...methods}>
        <p className='text-md text-center mb-2'>{t('auth.sendCodeText')}</p>
        <form onSubmit={handleSubmit(onSubmit)} className='flex flex-col justify-center'
            noValidate>
            {input.email}
            <div className='flex justify-center mt-1 sm:mt-1'>
                <Button name={t('button.send')} type="submit" style='py-2 bg-blue-500 text-white hover:bg-blue-400' />
            </div>
        </form>
    </FormProvider>
}

export default ForgotPasswordEmailForm;