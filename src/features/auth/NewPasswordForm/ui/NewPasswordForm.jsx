import { FormProvider, useForm } from "react-hook-form";
import inputData from "../model/inputData";
import useNewPasswordForm from "../model/useNewPasswordForm";
import Button from "../../../../shared/ui/button/Button.jsx";
import {useTranslation} from "react-i18next";

const NewPasswordForm = () => {
    const methods = useForm();
    const { handleSubmit } = methods;
    const { onSubmit } = useNewPasswordForm();
    const {t} = useTranslation()
    const inputs = inputData(t)
    return <FormProvider {...methods}>
        <form onSubmit={handleSubmit(onSubmit)} className='flex flex-col justify-center mt-5' noValidate>
            {inputs.NewPassword}
            {inputs.NewPasswordConfirm}
            <div className='flex justify-center mt-1'>
                <Button name={t('button.send')} type="submit" style='py-2 bg-blue-500 text-white hover:bg-blue-400' />
            </div>
        </form>
    </FormProvider>
}

export default NewPasswordForm;