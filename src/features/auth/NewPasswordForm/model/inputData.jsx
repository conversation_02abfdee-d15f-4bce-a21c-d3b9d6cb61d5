import PasswordField from "shared/ui/inputField/PasswordInput";

const inputData = (t) => ({
    NewPassword: <PasswordField
        name="NewPassword"
        label={t('auth.newPassword')}
        placeholder={t('auth.enterNewPassword')}
        required
    />,
    NewPasswordConfirm:
        <PasswordField
            name="NewPasswordConfirm"
            label={t('auth.confirmNewPassword')}
            placeholder={t('auth.confirmNewPassword')}
            required
        />
})

export default inputData;