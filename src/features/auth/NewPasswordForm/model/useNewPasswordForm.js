import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { createNewPassword } from "store/actions/auth/Auth";

const useNewPasswordForm = () => {
  const dispatch = useDispatch()

  const navigate = useNavigate()
  const email = useSelector(state => state.auth.email);

  const onSubmit = async (data) => {
    try {
      const result = await dispatch(createNewPassword({
        Email: email,
        NewPassword: data.NewPassword,
        NewPasswordConfirm: data.NewPasswordConfirm
      }));

      if (createNewPassword.fulfilled.match(result)) {
        navigate('/auth/login');
        dispatch(verifyCode(''))
      }
    } catch (e) {
      console.log('Error occurred:', e);
    }
  };
  return {
    onSubmit
  };
};

export default useNewPasswordForm;
