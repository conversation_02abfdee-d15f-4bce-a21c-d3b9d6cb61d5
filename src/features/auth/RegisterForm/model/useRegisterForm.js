import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import useGoogleAuthorization from "shared/hooks/useGoogleAuthorization";
import {register} from "store/actions/auth/Auth";

const useRegisterForm = () => {
    const dispatch = useDispatch();
    const navigate = useNavigate();

    const onSubmit = data => {
        dispatch(register({ ...data, navigate }));
    };

    const sendGoogleLogin = useGoogleAuthorization();
    return { onSubmit, sendGoogleLogin }
};

export default useRegisterForm;
