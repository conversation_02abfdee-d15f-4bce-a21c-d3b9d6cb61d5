import InputField from "shared/ui/inputField/InputField";
import PasswordField from "shared/ui/inputField/PasswordInput";
import React from "react";

const inputData = (t) => ({
    email:  <InputField
        label={t('auth.email')}
        name='email'
        type='email'
        placeholder={t('auth.enterEmail')}
        required
    />,
    password: <PasswordField
        name="password"
        label={t('auth.password')}
        placeholder={t('auth.enterPassword')}
        required
    />,
    confirmPassword: <PasswordField
        name="passwordConfirm"
        label={t('auth.confirmYourPassword')}
        placeholder={t('auth.confirmYourPassword')}
        required
        rules={{
            validate: (value, f) => {
                return value === f.password || t('auth.passwordsDontMatch');
            },
        }}
    />,
    nickname:  <InputField
        label={t('auth.nickname')}
        name='nickname'
        type='text'
        placeholder={t('auth.enterNickname')}
        required
    />,
});

export default inputData;
