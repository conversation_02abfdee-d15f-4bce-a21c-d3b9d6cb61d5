import { Form<PERSON>rovider, useForm } from 'react-hook-form';
import { useDispatch } from 'react-redux';
import {Link, Navigate, useNavigate} from 'react-router-dom';
import { FaGoogle } from "react-icons/fa";
import {useTranslation} from "react-i18next";
import React from "react";
import useRegisterForm from "../model/useRegisterForm.js";
import inputData from "../model/inputData.jsx";
import Button from "../../../../shared/ui/button/Button.jsx";

const Register = () => {
    const methods = useForm();
    const { handleSubmit } = methods;
    const dispatch = useDispatch();
    const { onSubmit, sendGoogleLogin } = useRegisterForm();
    const {t} = useTranslation()
    const inputs = inputData(t);


    return <FormProvider {...methods}>
                <form onSubmit={handleSubmit(onSubmit)} className='flex flex-col justify-center' noValidate>
                    {inputs?.email}
                    {inputs?.password}
                    {inputs?.confirmPassword}
                    {inputs?.nickname}
                    <div className='flex flex-col gap-2 mt-5'>
                        <Button name={t('auth.register')} type="submit" style='py-2 bg-blue-500 text-white hover:bg-blue-400' />
                        <p className='text-center'>{t('common.or')}</p>
                        <Button
                            name={t('auth.signGoogle')}
                            type='button'
                            onClick={sendGoogleLogin}
                            style='py-2 bg-white text-black hover:bg-blue-400 hover:text-white'
                            iconComponent={<FaGoogle/>}
                            iconStyle='left-20'
                        />
                    </div>
                    <p className='mt-5 text-md font-medium'>{t('auth.doYouHaveAccount')}
                        <Link to='/auth/login' className='font-bold ml-2'>{t('auth.login')}</Link>
                    </p>
                    {/*{authError && <p>{authError}</p>}*/}
                </form>
            </FormProvider>
};

export default Register;
