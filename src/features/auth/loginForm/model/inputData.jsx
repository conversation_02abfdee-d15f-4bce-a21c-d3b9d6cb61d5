import InputField from "shared/ui/inputField/InputField";
import PasswordField from "shared/ui/inputField/PasswordInput";

const inputData = (t) => ({
    email: <InputField
        label={t('auth.email')}  // Используем переводы для label
        name='email'
        type='email'
        placeholder={t('auth.enterEmail')}  // Используем переводы для placeholder
        required
    />,
    password: <PasswordField
        name="password"
        label={t('auth.password')}  // Используем переводы для label
        placeholder={t('auth.enterPassword')}  // Используем переводы для placeholder
        required
    />,
});

export default inputData;
