import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import useGoogleAuthorization from "shared/hooks/useGoogleAuthorization";
import { login } from "store/actions/auth/Auth";

const useLoginForm = () => {
    const dispatch = useDispatch();
    const navigate = useNavigate();

    const onSubmit = data => {
        dispatch(login({ ...data, navigate }));
    };

    const sendGoogleLogin = useGoogleAuthorization();
    return { onSubmit, sendGoogleLogin }
};

export default useLoginForm;
