import { useForm, FormProvider } from 'react-hook-form';
import { Link } from 'react-router-dom';
import Button from 'shared/ui/button/Button';
import { FaGoogle } from 'react-icons/fa';
import inputData from '../model/inputData'; 
import useLoginForm from '../model/useLoginForm';
import {useTranslation} from "react-i18next";

const LoginForm = () => {
    const methods = useForm();
    const { handleSubmit } = methods;
    const { onSubmit, sendGoogleLogin } = useLoginForm();
    const {t} = useTranslation();
    const inputs = inputData(t);

    return <FormProvider {...methods}>
        <form onSubmit={handleSubmit(onSubmit)} className='flex flex-col justify-center gap-2' noValidate>
            {inputs?.email}
            {inputs?.password}
            <p className='mb-3 text-right font-medium cursor-pointer -mt-2'>
                <Link to="/auth/forgot-password/email-forgot-password">
                    {t('auth.forgotYourPassword')}
                </Link>
            </p>
            <div className='flex flex-col gap-2'>
                <Button name={t('auth.login')} type="submit" style='py-3 bg-blue-500 text-white hover:bg-blue-400' />
                <p className='text-center'>или</p>
                <Button
                    name={t('auth.signGoogle')}
                    type='button'
                    onClick={sendGoogleLogin}
                    style='py-3 bg-white text-black hover:bg-blue-400 hover:text-white'
                    iconComponent={<FaGoogle />}
                    iconStyle='left-20'
                />
            </div>
            <p className='mt-5 text-md font-medium'>{t('auth.dontHaveAccount')}
                <Link to='/auth/register' className='font-bold ml-2'>{t('auth.register')}</Link>
            </p>
            {/*{authError && <p>{authError}</p>}*/}
        </form>
    </FormProvider>
}

export default LoginForm; 