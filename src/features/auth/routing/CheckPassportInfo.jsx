import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import Modal from "shared/ui/modal/Modal.jsx";
import {useTranslation} from "react-i18next";

const CheckPassportInfo = ({ children }) => {
    const user = useSelector(state => state.auth.user.user);
    const navigate = useNavigate();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const {t} = useTranslation()

    useEffect(() => {
        if (user.Role === 'client' && !isModalOpen) {
            setIsModalOpen(true);
        }
    }, [user, isModalOpen]);

    const handleClose = () => {
        setIsModalOpen(false);
        navigate('/profile');
    };

    const handleConfirm = () => {
        setIsModalOpen(false);
        navigate('/profile/add-passport');
    };

    if (isModalOpen) {
        return (
            <Modal
                isOpen={isModalOpen}
                onClose={handleClose}
                onConfirm={handleConfirm}
                title={t('profilePage.FillPassportDetails')}
                message={t('profilePage.message')}
                confirmText={t('button.fillIn')}
                cancelText={t('button.cancel')}
            />
        );
    }
    return children;

}

export default CheckPassportInfo;
