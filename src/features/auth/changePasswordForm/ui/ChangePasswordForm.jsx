import { FormProvider, useForm } from "react-hook-form";
import inputData from "../model/inputData";
import useChangePasswordForm from "../model/useChangePasswordForm";
import Button from "../../../../shared/ui/button/Button.jsx";

const ChangePasswordForm = () => {
    const methods = useForm();
    const { handleSubmit, watch } = methods;
    const { onSubmit } = useChangePasswordForm();
    return <FormProvider {...methods}>
        <form onSubmit={handleSubmit(onSubmit)} className='flex flex-col justify-center gap-2' noValidate>
            {inputData.CurrentPassword}
            {inputData.NewPassword}
            {inputData.NewPasswordConfirm}
            <div className='flex justify-center mt-1 sm:mt-3'>
                <Button name='Отправить' type="submit" style='py-2 bg-blue-500 text-white hover:bg-blue-400' />
            </div>
        </form>
    </FormProvider>
}

export default ChangePasswordForm;