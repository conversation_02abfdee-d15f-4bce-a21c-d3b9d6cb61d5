import PasswordField from "shared/ui/inputField/PasswordInput";

const inputData = {
    CurrentPassword: <PasswordField
        name="CurrentPassword"
        placeholder='Введите старый пароль'
        label="Старый пароль"
        required
    />,
    NewPassword:
        <PasswordField
            name="NewPassword"
            placeholder='Введите новый пароль'
            label="Новый пароль"
            required
            rules={{
                validate: (value, f) => {
                    return value !== f.CurrentPassword || 'Новый пароль не должен быть таким же, как старый';
                },
            }}
        />,
    NewPasswordConfirm: <PasswordField
        name="NewPasswordConfirm"
        label="Подтвердите новый пароль"
        placeholder='Подтвердите новый пароль'
        required
        rules={{
            validate: (value, f) => {
                return value === f.NewPassword || 'Пароли не совпадают';
            },
        }}
    />
}

export default inputData;