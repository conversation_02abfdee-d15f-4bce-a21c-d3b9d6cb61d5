import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { changePassword } from 'store/actions/auth/Auth';

const useChangePasswordForm = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const onSubmit = (data) => {
    dispatch(changePassword({ values: data, navigate }))
  }
  return {
    onSubmit,
  };
};

export default useChangePasswordForm;
