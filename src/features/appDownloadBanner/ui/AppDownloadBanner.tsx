import { useEffect, useState } from "react";

function AppDownloadBanner() {
    const [showBanner, setShowBanner] = useState(false);

    useEffect(() => {
        const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
        const isClosed = localStorage.getItem('app_banner_closed');

        if (isMobile && !isClosed) {
            setShowBanner(true);
        }
    }, []);

    const handleClose = () => {
        localStorage.setItem('app_banner_closed', 'true');
        setShowBanner(false);
    };

    if (!showBanner) return null;

    return (
        <div className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t shadow-md px-4 pb-10 pt-8 flex items-center justify-center">
            <div className="flex items-center justify-center gap-3">
                <div className="flex items-center justify-center flex-col gap-2">
                    <p className="font-semibold text-center">Скачайте наше мобильное приложение</p>
                    <div className="flex gap-2 mt-1">
                        <a
                            href="https://play.google.com/store/apps/details?id=com.joneleumka.togolock"
                            target="_blank"
                            rel="noopener noreferrer"
                        >
                            <img src="/googlePlay.svg" alt="Google Play" className="object-cover w-[120px] h-[40px]" />
                        </a>
                        <a
                            href="https://apps.apple.com/kg/app/togolock/id6742218319"
                            target="_blank"
                            rel="noopener noreferrer"
                        >
                            <img src="/appStore.svg" alt="App Store" className="object-cover w-[120px] h-[40px]" />
                        </a>
                    </div>
                </div>
            </div>
            <button onClick={handleClose} className="text-gray-500 hover:text-black text-3xl font-bold px-2 absolute top-1 right-2 z-50">
                ×
            </button>
        </div>
    );
}

export default AppDownloadBanner;
