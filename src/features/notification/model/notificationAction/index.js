// src/entities/notification/service/notificationService.js
import axios from 'axios';
import {API} from 'shared/config/api.js'
import {createAsyncThunk} from "@reduxjs/toolkit";
import axiosInstance from "../../../../shared/libs/AxiosInstance/index.js";


export const fetchUserAllNotifications = createAsyncThunk(
    'notifications/fetchUserAllNotifications',
    async ({ page, page_size, order_by, search }) => {
        try {
            const response = await axiosInstance.get(`${API}notification/user_notification`, {
                params: {
                    page,
                    page_size,
                    order_by,
                    search,
                },
            });
            return response.data;
        } catch (error) {
            console.error('Error fetching user notifications:', error);
            throw error;
        }
    }
);


export const markNotificationAsRead = createAsyncThunk(
    'notifications/markAsRead',
    async (notificationId) => {
        try {
            await axiosInstance.patch(`${API}notification/read/${notificationId}`);
            return notificationId;
        } catch (error) {
            console.error('Error marking notification as read:', error);
            throw error;
        }
    }
);
