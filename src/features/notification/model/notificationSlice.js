import { createSlice } from '@reduxjs/toolkit';

import {markNotificationAsRead, fetchUserAllNotifications} from './notificationAction/index.js'

const initialState = {
    items: [],
    status: 'idle',
    error: null,
};

const notificationSlice = createSlice({
    name: 'notifications',
    initialState,
    reducers: {
        setNotifications(state, action) {
            state.items = action.payload;
        },
        addNotification(state, action) {
            const newNotifications = action.payload;
            // Проверяем, есть ли уже уведомление с таким же ID
            newNotifications.forEach(newNotification => {
                const existingNotification = state.items.find(item => item.ID === newNotification.ID);
                if (!existingNotification) {
                    state.items.push(newNotification);
                }
            });
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(markNotificationAsRead.pending, (state) => {
                state.status = 'loading';
                state.error = null;
            })
            .addCase(markNotificationAsRead.fulfilled, (state, action) => {
                state.status = 'succeeded';
                const notification = state.items.find(item => item.ID === action.payload);
                if (notification) {
                    notification.IsRead = true;
                }
            })
            .addCase(markNotificationAsRead.rejected, (state, action) => {
                state.status = 'failed';
                state.error = action.error.message;
            })
            .addCase(fetchUserAllNotifications.pending, (state) => {
                state.status = 'loading';
                state.error = null;
             })
            .addCase(fetchUserAllNotifications.fulfilled, (state, action) => {
                state.status = 'succeeded';
                state.items = action.payload;

            })
            .addCase(fetchUserAllNotifications.rejected, (state, action) => {
                state.status = 'failed';
                state.error = action.error.message;
            });
    },
});

export const { setNotifications, addNotification } = notificationSlice.actions;

export default notificationSlice.reducer;
