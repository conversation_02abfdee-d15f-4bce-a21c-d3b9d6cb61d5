import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { IoIosNotificationsOutline, IoIosCheckmarkCircleOutline } from 'react-icons/io';
import useActiveOutsideClick from 'shared/hooks/useOutsideClick';
import { fetchUserAllNotifications, markNotificationAsRead } from '../model/notificationAction/index.js';
import useWebSocket from "shared/hooks/useWebSocket.js";
import { addNotification } from "../model/notificationSlice.js";
import {Link, useNavigate} from "react-router-dom";
import { formatCreatedAt } from 'shared/utils/date-fns';
import {useTranslation} from "react-i18next";

const getNotificationRoute = (notification) => {
    const routes = {
        "У вас новая заявка на бронирование": { path: "/owner/application" },
        "Новая заявка на бронирование": { path: "/owner/application" },
        "Бронирование одобрено": { path: "/profile/my-bookings", state: { status: 'active' } },
        "Бронирование отклонено": { path: "/profile/my-bookings", state: { status: 'rejected' } },
        // Можно добавить новые типы уведомлений и соответствующие маршруты
    };

    return routes[notification.Notification.Title] || { path: "/notification" };  // Если тип уведомления не найден, возвращаем путь на страницу уведомлений
};


const Notification = () => {
    const dispatch = useDispatch();
    const { ref, isActive, setIsActive } = useActiveOutsideClick(false);
    const notifications = useSelector(state => state.notifications.items);
    const unreadNotifications = notifications.filter(notification => !notification.IsRead);
    const { t } = useTranslation();
    const navigate = useNavigate();


    const handleClick = () => {
        if (window.innerWidth < 768) {
            navigate('/notification');
        } else {
            setIsActive(!isActive);
        }
    };

    const handleNewNotification = (notification) => {
        dispatch(addNotification(notification));
    };

    useEffect(() => {
        dispatch(fetchUserAllNotifications({ search: '{"is_read":false}' }))
    }, [dispatch])

    useWebSocket(handleNewNotification);

    const handleNotificationClick = async (notification) => {
        await dispatch(markNotificationAsRead(notification.ID));

        const { path, state } = getNotificationRoute(notification);
        navigate(path, state ? { state } : {});

        setIsActive(false);
    };

    return (
        <div className='relative' ref={ref}>
            <div className='cursor-pointer' onClick={handleClick}>
                <img src='/notificationIcon.svg' alt='img' />
            </div>
            {unreadNotifications.length > 0 && (
                <div
                    className='absolute h-6 w-6 -top-4 -right-3 flex items-center justify-center rounded-full bg-amber-400'>
                    {unreadNotifications.length}
                </div>
            )}
            {isActive && (
                <div
                    className='absolute w-[300px] z-50 -right-5 md:-right-15 xl:-right-20 mt-2 bg-white shadow-lg rounded-lg'>
                    <div className='max-h-[300px] overflow-auto flex flex-col'>
                        {unreadNotifications.length > 0 ? (
                            unreadNotifications.slice().reverse().map(notification => (
                                <div
                                    className={`flex items-center gap-2 w-full px-2 md:pr-5 py-2 border-b cursor-pointer hover:bg-gray-100 ${notification.IsRead ? 'bg-gray-100' : ''}`}
                                    key={notification.ID}
                                    onClick={() => handleNotificationClick(notification)}
                                >
                                    <div>
                                        <IoIosCheckmarkCircleOutline size={20} style={{ color: "#00ff40" }} />

                                    </div>
                                    <div className='flex flex-col'>
                                        <span className='text-sm'>{notification.Notification.Text}</span>
                                        <span
                                            className='text-xs font-light'>{formatCreatedAt(notification.CreatedAt)}</span>
                                    </div>
                                </div>
                            ))
                        ) : (
                            <div className='flex flex-col items-center gap-2 w-full px-2 md:pr-5 py-2 border-b'>
                                <span className='font-semibold text-center p-3 sm:p-4 border-b'>Уведомлений нет</span>
                                <Link className='font-semibold text-center' to='/notification'
                                    onClick={() => setIsActive(false)}>{t('notification.seeAll')}</Link>
                            </div>
                        )}
                    </div>
                    {unreadNotifications.length > 0 && (
                        <div className='text-center p-1 border-t'>
                            <Link className='font-semibold text-center text-sm' to='/notification'
                                onClick={() => setIsActive(false)}>{t('notification.seeAll')}</Link>
                        </div>
                    )}
                </div>
            )}
        </div>
    );
};

export default Notification;
