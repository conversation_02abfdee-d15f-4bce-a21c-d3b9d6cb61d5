import React, { useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ayer, useMap } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import 'leaflet.markercluster/dist/MarkerCluster.css';
import 'leaflet.markercluster/dist/MarkerCluster.Default.css';
import L from 'leaflet';
import 'leaflet.markercluster';
import {API_IMG} from "../../shared/config/api.js";
import {useNavigate} from "react-router-dom";


delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
    iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
    iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
    shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

const MarkerCluster = ({ listings }) => {
    const map = useMap();
    const navigate = useNavigate();

    console.log(listings, 'listings');

    useEffect(() => {
        const markers = L.markerClusterGroup();

        listings.forEach(item => {
            const lat = parseFloat(item.Latitude);
            const lng = parseFloat(item.Longitude);

            if (!isNaN(lat) && !isNaN(lng)) {
                const marker = L.marker([lat, lng]).bindPopup(`
                      <a href="/accommodation/${item.ID}" style="text-decoration: none; color: inherit;">
                        <div style="max-width: 200px">
                          <img
                            src="${item.Images.length > 0 ? API_IMG + item.Images[0].ImageUrl : '/noImg.jpg'}"
                            alt="img"
                            style="width: 100%; height: 120px; object-fit: cover; border-radius: 8px;"
                          />
                          <div style="margin-top: 8px; font-weight: bold;">${item.Title}</div>
                          <div>${item.LocationLabel}</div>
                        </div>
                      </a>
                    `);
                markers.addLayer(marker);
            }
        });

        map.addLayer(markers);

        map.on('popupopen', (e) => {
            const container = e.popup.getElement();
            const link = container.querySelector('a[href^="/accommodation/"]');
            if (link) {
                link.addEventListener('click', (event) => {
                    event.preventDefault(); // отменяем переход по ссылке
                    const href = link.getAttribute('href');
                    if (href) navigate(href);
                });
            }
        });


        return () => {
            map.removeLayer(markers);
        };
    }, [map, listings]);

    return null;
};

const MapSearch = ({ listings }) => {
    return (
        <div className="w-full h-full">
            <MapContainer
                center={[42.8748, 74.5932]}
                zoom={12}
                scrollWheelZoom
                zoomControl={false}
                style={{ height: '100%', width: '100%' }}
            >
                <TileLayer
                    url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                />
                <MarkerCluster listings={listings} />
            </MapContainer>
        </div>
    );
};

export default MapSearch;
