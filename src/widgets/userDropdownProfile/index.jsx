import { useDispatch, useSelector } from 'react-redux';
import { logout } from "../../store/actions/auth/Auth.js";
import {Link, useNavigate} from 'react-router-dom';
import { FaUserCog, FaSignOutAlt, <PERSON>a<PERSON>ey, FaUser } from 'react-icons/fa';
import useActiveOutsideClick from "../../shared/hooks/useOutsideClick.js";
import UserImage from "shared/assets/user.png";
import React from "react";
import { API_IMG } from "../../shared/config/api.js";
import { useTranslation } from "react-i18next";

const UserProfileMenu = () => {
    const dispatch = useDispatch();
    const user = useSelector(state => state.auth?.user);
    const { ref, isActive, setIsActive } = useActiveOutsideClick(false);
    const { t } = useTranslation();
    const navigate = useNavigate();


    const handleLogout = () => {
        dispatch(logout({ refresh_token: user.token }));
    };

    const handleImageError = (e) => {
        e.target.src = UserImage;
    };

    const handleClick = () => {
        if (window.innerWidth < 768) {
            navigate('/profile');
        } else {
            setIsActive(!isActive);
        }
    };

    return (
        <div className='relative' ref={ref}>
            <div className='flex items-center cursor-pointer' onClick={handleClick}>
                {/*<span className='hidden sm:block mr-2 font-semibold'>{user.user?.Profile?.Nickname}</span>*/}
                <img
                    src={(user.user?.Profile?.Avatar ? API_IMG + user.user?.Profile?.Avatar : UserImage)}
                    alt="User Profile"
                    className='w-8 h-8 rounded-full object-cover'
                    onError={handleImageError}
                />
            </div>
            {isActive && (
                <div className='absolute z-50 top-[45px] right-0 mt-2 bg-white shadow-lg rounded-lg w-max' onClick={() => setIsActive(false)}>
                    <ul>
                        <li className='sm:hidden flex items-center px-4 py-2 text-md border-b'>
                            <span className='ml-2 font-medium'>{user?.email}</span>
                        </li>
                        <li className='hover:bg-gray-100'>
                            <Link to='/profile' className='flex items-center px-4 py-2 text-md'>
                                <FaUser className='mr-2' />
                                {t('profilePage.profile')} {/* Перевод для профиля */}
                            </Link>
                        </li>
                        <li className='hover:bg-gray-100'>
                            <Link to='/profile/settings' className='flex items-center px-4 py-2 text-md'>
                                <FaUserCog className='mr-2' />
                                {t('profilePage.settings')} {/* Перевод для настроек */}
                            </Link>
                        </li>
                        <li className='hover:bg-gray-100'>
                            <Link to='/auth/change-password' className='flex items-center px-4 py-2 text-md'>
                                <FaKey className='mr-2' />
                                {t('profilePage.changePassword')} {/* Перевод для смены пароля */}
                            </Link>
                        </li>
                        <li>
                            <button onClick={handleLogout} className='flex items-center w-full text-left px-4 py-2 text-md'>
                                <FaSignOutAlt className='mr-2' />
                                {t('profilePage.logout')} {/* Перевод для выхода */}
                            </button>
                        </li>
                    </ul>
                </div>
            )}
        </div>
    );
};

export default UserProfileMenu;
