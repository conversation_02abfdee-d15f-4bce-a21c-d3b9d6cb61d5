import React, {useEffect, useState} from 'react';
import {useNavigate, useParams} from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import Button from "../../../../shared/ui/button/Button.jsx";
import {API_IMG} from "../../../../shared/config/api.js";
import {
    approveUser, deleteUser,
    getUserById, rejectUserDeletion,
} from "../../../../store/actions/admin/adminListAction.js";
import Modal from "../../../../shared/ui/modal/Modal.jsx";
import UserImage from "../../../../shared/assets/user.png";

const UserDeleteRequestDetails = () => {
    const { id } = useParams();
    const dispatch = useDispatch();
    const navigate = useNavigate()

    const user = useSelector(state => state.admin?.userById);
    const [open, setOpen] = React.useState(false);
    const [rejectionReason, setRejectionReason] = useState("");


    useEffect(() => {
        if (id) {
            dispatch(getUserById(id));
        }
    }, [dispatch, id]);


    const handleConfirmDelete = async () => {
        try {
            await dispatch(deleteUser(id)).unwrap();
            navigate('/admin/applications');
        } catch (error) {
            console.error("Ошибка при подтверждении:", error);
        }
    };


    const handleCancel = () => {
        setOpen(true)
    }

    const handleReject = async () => {
        if (!rejectionReason.trim()) {
            alert("Пожалуйста, укажите причину отказа.");
            return;
        }
        try {
            await dispatch(rejectUserDeletion({ id, rejection_reason: rejectionReason })).unwrap();
            setOpen(false);
            navigate('/admin/applications');
        } catch (error) {
            console.error("Ошибка при отклонении:", error);
        }
    };


    return (
        <div>
            <div className='flex gap-2 px-5 cursor-pointer'>
                <img src='/backArrow.svg' alt='icon'/>
                <p>Вернутся</p>
            </div>
            <div className="p-5 flex gap-10">
                <div className="">
                    <div className='bg-gray-200 flex items-center justify-center p-5 rounded-md'>
                        <img
                            src={user?.Profile?.Avatar ? API_IMG+user?.Profile?.Avatar : UserImage}
                            className='rounded-full w-[150px] h-[150px] md:w-40 md:h-40 object-cover'
                            alt='img'
                        />
                    </div>
                </div>


                <div className="w-2/3">
                    <h1 className="text-2xl font-bold text-gray-800 mb-5">Запрос на удаление</h1>

                    {/* Форма заявки */}
                    <div className="grid grid-cols-2 gap-4">
                        <div>
                            <p className="text-sm text-gray-500">ФИО</p>
                            <input
                                type="text"
                                value={`${user?.Profile?.Surname || ''} ${user?.Profile?.Name || ''} ${user?.Passport?.Patronymic || ''}`.trim()}
                                className="w-full p-2 border rounded-md"
                                readOnly
                            />
                        </div>
                        <div>
                            <p className="text-sm text-gray-500">Дата рождения</p>
                            <input type="text" value={user?.Profile?.Passport?.DateOfBirth || ''} className="w-full p-2 border rounded-md" readOnly />
                        </div>
                        <div>
                            <p className="text-sm text-gray-500">Национальность</p>
                            <input type="text" value={user?.Profile?.Passport?.Nationality || ''} className="w-full p-2 border rounded-md" readOnly />
                        </div>
                        <div>
                            <p className="text-sm text-gray-500">Дата выдачи</p>
                            <input type="text" value={user?.Profile?.Passport?.DateOfIssue || ''} className="w-full p-2 border rounded-md" readOnly />
                        </div>
                        <div>
                            <p className="text-sm text-gray-500">Дата истечения срока</p>
                            <input type="text" value={user?.Profile?.Passport?.DateOfExpiry || ''} className="w-full p-2 border rounded-md" readOnly />
                        </div>
                        <div>
                            <p className="text-sm text-gray-500">Номер документа</p>
                            <input type="text" value={user?.Profile?.Passport?.DocumentNumber || ''} className="w-full p-2 border rounded-md" readOnly />
                        </div>
                        <div>
                            <p className="text-sm text-gray-500">Орган выдачи</p>
                            <input type="text" value={user?.Profile?.Passport?.Authority || ''} className="w-full p-2 border rounded-md" readOnly />
                        </div>
                        <div>
                            <p className="text-sm text-gray-500">ПИН</p>
                            <input type="text" value={user?.Profile?.Passport?.PIN || ''} className="w-full p-2 border rounded-md" readOnly />
                        </div>
                    </div>

                    <div className="flex justify-end gap-4 mt-6">
                        <Button
                            style="border border-red-500 text-red-500 px-4 py-2 rounded-md"
                            name="Отклонить"
                            onClick={handleCancel} />
                        <Button
                            style="bg-blue-500 text-white px-4 py-2 rounded-md"
                            name="Подтвердить"
                            onClick={handleConfirmDelete}
                        />
                    </div>
                    <Modal
                        isOpen={open}
                        onClose={() => setOpen(false)}
                        title='Укажите причину отказа'
                        buttonHidde={false}
                        style="w-1/2 rounded-xl"
                    >
                        <textarea  className="w-full p-2 border rounded-md h-48 max-h-52 custom-scrollbar focus:outline-none"
                                   onChange={(e) => setRejectionReason(e.target.value)}
                        />
                        <div className='flex justify-end gap-5 mt-5'>
                            <Button style="border border-red-500 text-red-500 px-4 py-2 rounded-md" name='Закрыть' onClick={() => setOpen(false)}/>
                            <Button style="bg-blue-500 text-white px-4 py-2 rounded-md" name='Отправить' onClick={handleReject}/>
                        </div>
                    </Modal>
                </div>
            </div>
        </div>
    );
};

export default UserDeleteRequestDetails;
