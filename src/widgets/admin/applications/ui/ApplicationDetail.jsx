import React, {useEffect, useState} from 'react';
import {useNavigate, useParams} from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import DetailPageSkeleton from "../../../../shared/ui/skeleton/detailPageSkeleton/DetailPageSkeleton";
import {getAccommodationByID} from "../../../../store/actions/accommodations/Accommodation.ts";
import Button from "../../../../shared/ui/button/Button.jsx";
import {API_IMG} from "../../../../shared/config/api.js";
import {unverifyAccommodation, verifyAccommodation} from "../../../../store/actions/admin/adminListAction.js";
import Modal from "../../../../shared/ui/modal/Modal.jsx";

const ApplicationDetail = () => {
    const { id } = useParams();
    const dispatch = useDispatch();
    const navigate = useNavigate()

    const application = useSelector(state => state.admin?.accommodationById);
    const loading = useSelector(state => state.admin?.loading);
    const [open, setOpen] = React.useState(false);
    const [rejectionReason, setRejectionReason] = useState("");


    useEffect(() => {
        if (id) {
            dispatch(getAccommodationByID(id));
        }
    }, [dispatch, id]);


    const handleConfirm = async () => {
        try {
            dispatch(verifyAccommodation(id))
            navigate('/admin/applications')
        } catch (error) {
            console.error("Ошибка при подтверждении:", error);
        }
    }

    const handleCancel = () => {
        setOpen(true)
    }

    const handleReject = async () => {
        if (!rejectionReason.trim()) {
            alert("Пожалуйста, укажите причину отказа.");
            return;
        }
        try {
            await dispatch(unverifyAccommodation({ id, rejectionReason })).unwrap();
            setOpen(false);
            navigate('/admin/applications');
        } catch (error) {
            console.error("Ошибка при отклонении:", error);
        }
    };


    return (
        <div className="p-5 flex gap-10">
            {/* Левая часть с изображениями */}
            <div className="w-1/3 flex flex-col gap-4 max-h-[600px] overflow-y-auto custom-scrollbar">
                {application?.Images?.length > 0 ? (
                    application.Images.map((image, index) => (
                            <img
                                src={`${API_IMG}${image.ImageUrl}`}
                                alt="img"
                                className="w-full h-52 object-cover"
                                key={image.ID}
                            />
                    ))
                ) : (
                    [...Array(3)].map((_, index) => (
                        <div key={index} className="w-full h-32 bg-gray-200 rounded-md"></div>
                    ))
                )}
            </div>


            {/* Правая часть с формой */}
            <div className="w-2/3">
                <h1 className="text-2xl font-bold text-gray-800 mb-5">Название</h1>

                {/* Форма заявки */}
                <div className="grid grid-cols-2 gap-4">
                    <div>
                        <p className="text-sm text-gray-500">Категория</p>
                        <input type="text" value={application?.Category?.NameRu || ''} className="w-full p-2 border rounded-md" readOnly />
                    </div>
                    <div>
                        <p className="text-sm text-gray-500">Страна</p>
                        <input type="text" value={application?.Country || ''} className="w-full p-2 border rounded-md" readOnly />
                    </div>
                    <div>
                        <p className="text-sm text-gray-500">Город</p>
                        <input type="text" value={application?.City || ''} className="w-full p-2 border rounded-md" readOnly />
                    </div>
                    <div>
                        <p className="text-sm text-gray-500">Улица/дом</p>
                        <input type="text" value={application?.LocationLabel || ''} className="w-full p-2 border rounded-md" readOnly />
                    </div>
                    <div>
                        <p className="text-sm text-gray-500">Количество комнат</p>
                        <input type="text" value={application?.RoomsQuantity || ''} className="w-full p-2 border rounded-md" readOnly />
                    </div>
                    <div>
                        <p className="text-sm text-gray-500">Количество гостей</p>
                        <input type="text" value={application?.PeopleQuantity || ''} className="w-full p-2 border rounded-md" readOnly />
                    </div>
                    <div>
                        <p className="text-sm text-gray-500">Цена за сутки</p>
                        <input type="text" value={application?.Price || ''} className="w-full p-2 border rounded-md" readOnly />
                    </div>
                    <div className="col-span-2">
                        <p className="text-sm text-gray-500">Описание</p>
                        <textarea value={application?.Description || ''} className="w-full p-2 border rounded-md h-32" readOnly />
                    </div>
                </div>

                <div className="flex justify-end gap-4 mt-6">
                    <Button
                        style="border border-red-500 text-red-500 px-4 py-2 rounded-md"
                        name="Отклонить"
                        onClick={handleCancel} />
                    <Button
                        style="bg-blue-500 text-white px-4 py-2 rounded-md"
                        name="Подтвердить"
                        onClick={handleConfirm}
                    />
                </div>
                    <Modal
                        isOpen={open}
                        onClose={() => setOpen(false)}
                        title='Укажите причину отказа'
                        buttonHidde={false}
                        style="w-1/2 rounded-xl"
                    >
                        <textarea  className="w-full p-2 border rounded-md h-48 max-h-52 custom-scrollbar focus:outline-none"
                          onChange={(e) => setRejectionReason(e.target.value)}
                        />
                        <div className='flex justify-end gap-5 mt-5'>
                            <Button style="border border-red-500 text-red-500 px-4 py-2 rounded-md" name='Закрыть' onClick={() => setOpen(false)}/>
                            <Button style="bg-blue-500 text-white px-4 py-2 rounded-md" name='Отправить' onClick={handleReject}/>
                        </div>
                    </Modal>
            </div>
        </div>
    );
};

export default ApplicationDetail;
