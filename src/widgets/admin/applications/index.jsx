import React, {useEffect, useState} from 'react';
import {useDispatch, useSelector} from "react-redux";
import {useNavigate} from "react-router-dom";


import Table from "../../../shared/ui/table/Table.jsx";
import {getAllAccomodations, getAllUsers} from "../../../store/actions/admin/adminListAction.js";
import { FaEye} from "react-icons/fa";

function ApplicationsAdmins() {
    const navigate = useNavigate()
    const dispatch = useDispatch()

    const { Data: accommodationsData, Count: accommodationsCount } = useSelector(state => state.admin?.accommodations) || {};
    const { Data: usersData, Count: usersCount } = useSelector(state => state.admin?.users) || {};
    const [activeTab, setActiveTab] = useState("accommodation");
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 10;

    console.log(usersData, 'userData')


    useEffect(() => {
        if (activeTab === "accommodation") {
            dispatch(getAllAccomodations({ page: currentPage, limit: itemsPerPage, verification: "pending" }));
        } else if (activeTab === "owner") {
            dispatch(getAllUsers({ page: currentPage, limit: itemsPerPage, status: "pending" }));
        } else if (activeTab === "delete") {
            dispatch(getAllUsers({ page: currentPage, limit: itemsPerPage, delete_request: true }));
        }
    }, [dispatch, currentPage, activeTab]);


    const handleViewDetails = (row) => {
        if (activeTab === "accommodation") {
            navigate(`/admin/applications/show/${row.ID}`);
        } else if (activeTab === "owner") {
            navigate(`/admin/applications/user/show/${row.ID}`);
        } else if (activeTab === "delete") {
            navigate(`/admin/applications/user/delete-request/${row.ID}`);
        }
    };


    const columnsAccommodations  = [
        { field: 'ID', headerName: 'ID', width: '5%' },
        { field: 'Title', headerName: 'Название', width: '20%' },
        {
            field: 'Owner.Nickname',
            headerName: 'Владелец',
            cell: row => row.Owner?.Profile?.Nickname || 'Нет данных',
            width: '10%'
        },
        {
            field: 'Owner.PhoneNumber',
            headerName: 'Телефон',
            cell: row => row.Owner?.Profile?.PhoneNumber || 'Нет данных',
            width: '10%'
        },
        { field: 'LocationLabel', headerName: 'Локация', width: '15%' },
        {
            field: 'actions',
            headerName: 'Действия',
            cell: (row) => (
                <div className="flex items-center justify-center gap-3">
                    <button onClick={() => handleViewDetails(row)} className="text-gray-500 hover:text-gray-400">
                        <FaEye size={18} />
                    </button>
                </div>
            ),
            width: '5%'
        }
    ];

    const columnsUsers = [
        { field: 'ID', headerName: 'ID', width: '5%' },
        { field: 'Profile.Name', headerName: 'Имя пользователя', width: '20%' },
        { field: 'Profile.PhoneNumber', headerName: 'Контакты', width: '15%' },
        { field: 'Email', headerName: 'Email', width: '15%' },
        { field: 'CreatedAt', headerName: 'Дата', width: '10%' },
        {
            field: 'actions',
            headerName: 'Действия',
            cell: (row) => (
                <div className="flex items-center justify-center gap-3">
                    <button onClick={() => handleViewDetails(row)} className="text-gray-500 hover:text-gray-400">
                        <FaEye size={18} />
                    </button>
                </div>
            ),
            width: '5%'
        }
    ];

    const columnsDelete = [
        { field: 'ID', headerName: 'ID', width: '5%' },
        { field: 'Profile.Name', headerName: 'Имя пользователя', width: '20%' },
        { field: 'Profile.PhoneNumber', headerName: 'Контакты', width: '15%' },
        { field: 'Email', headerName: 'Email', width: '15%' },
        { field: 'UpdatedAt', headerName: 'Дата запроса', width: '15%' },
        {
            field: 'actions',
            headerName: 'Действия',
            cell: (row) => (
                <div className="flex items-center justify-center gap-3">
                    <button onClick={() => handleViewDetails(row)} className="text-gray-500 hover:text-gray-400">
                        <FaEye size={18} />
                    </button>
                </div>
            ),
            width: '5%'
        }
    ];




    const handlePageChange = (newPage) => {
        setCurrentPage(newPage);
    };


    return (
        <div>
            <h1 className='font-semibold text-xl'>Заявки</h1>
            <div className='flex gap-8 text-light-text cursor-pointer mb-4'>
                <p
                    className={`py-2 text-md ${activeTab === "owner" ? "border-b-2 border-blue-500" : "text-gray-500"}`}
                    onClick={() => setActiveTab("owner")}
                >
                    Регистрация владельца
                </p>
                <p
                    className={`py-2 text-md ${activeTab === "accommodation" ? "border-b-2 border-blue-500" : "text-gray-500"}`}
                    onClick={() => setActiveTab("accommodation")}
                >
                    Создание жилья
                </p>
                <p
                    className={`py-2 text-md ${activeTab === "delete" ? "border-b-2 border-blue-500" : "text-gray-500"}`}
                    onClick={() => setActiveTab("delete")}
                >
                    Удаление владельцев
                </p>
            </div>
            <Table
                columns={
                    activeTab === "accommodation" ? columnsAccommodations :
                        activeTab === "owner" ? columnsUsers :
                            columnsDelete
                }
                data={
                    activeTab === "accommodation"
                        ? accommodationsData
                        : activeTab === "delete"
                            ? usersData?.filter(user => user.delete_request)
                            : usersData
                }
                totalItems={activeTab === "accommodation" ? accommodationsCount : usersCount}
                itemsPerPage={itemsPerPage}
                currentPage={currentPage}
                onPageChange={handlePageChange}
            />

        </div>
    );
}

export default ApplicationsAdmins;