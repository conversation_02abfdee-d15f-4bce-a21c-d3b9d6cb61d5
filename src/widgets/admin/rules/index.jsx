import React, {useEffect, useState} from 'react';
import {useDispatch, useSelector} from "react-redux";
import Table from "../../../shared/ui/table/Table.jsx";
import {adminActions} from "../../../store/slices/admin/adminEntitySlice.js";
import DictionaryModal from "../../../features/admin/dictionary/CategoryModal.jsx";
import Button from "../../../shared/ui/button/Button.jsx";
import facilities from "../../../features/home/<USER>/facilities/index.jsx";

function RulesAdmins() {
    const dispatch = useDispatch();
    const { data: rules, status, error } = useSelector(
        state => state.adminEntities.rules
    );
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [selectedRules, setSelectedRules] = useState(null);

    const [page, setPage] = useState(1);
    const limit = 10;


    useEffect(() => {
        dispatch(adminActions.fetchRules({ page, limit }));
    }, [dispatch, page, limit]);

    const handleEditKey = (row) => {
        setSelectedRules(row)
        setIsModalOpen(true);
    };

    const handleAddRules = () => {
        setSelectedRules(null);
        setIsModalOpen(true);
    }

    const handleSaveRules = (rules) => {
        if (selectedRules) {
            const updatedCategory = {
                valueRu: rules.ru,
                valueKg: rules.kg,
                valueEn: rules.en,
                icon: rules.icon,
            };
            dispatch(adminActions.updateRule({ id: selectedRules.Id, updatedData: updatedCategory }))
                .then(() => dispatch(adminActions.fetchRules({ page, limit })));
        } else {
            const formattedCategory = {
                valueRu: rules.ru,
                valueKg: rules.kg,
                valueEn: rules.en,
                icon: rules.icon,
            };
            dispatch(adminActions.createRule(formattedCategory))
                .then(() => dispatch(adminActions.fetchRules({ page, limit })));
        }
        setIsModalOpen(false);
    };

    const deleteKey = (row) => {
        dispatch(adminActions.deleteRule(row.Id))
            .then(() => dispatch(adminActions.fetchRules({ page, limit })));
    };

    const columns = [
        { field: 'Id', headerName: 'ID', width: '5%' },
        { field: 'Value.ru', headerName: 'Русский', width: '15%' },
        { field: 'Value.kg', headerName: 'Кыргызский', width: '15%' },
        { field: 'Value.en', headerName: 'Английский', width: '15%' },
        { field: 'Icon', headerName: 'Иконки', width: '15%' },
        {
            field: 'actions',
            headerName: '',
            width: '5%',
            handleEdit: handleEditKey,
            handleDelete: deleteKey
        }
    ];

    const handlePageChange = (newPage) => {
        setPage(newPage);
    };

    return (
        <div>
            <div className='flex justify-between'>
                <h1 className='font-semibold text-xl'>Список правил</h1>
                <Button style='bg-blue-500 text-white' name='Добавить' onClick={handleAddRules}/>
            </div>
            <Table
                columns={columns}
                data={rules.Data}
                handleEdit={handleEditKey}
                handleDelete={deleteKey}
                totalItems={rules.Count}
                itemsPerPage={limit}
                currentPage={page}
                onPageChange={handlePageChange}
            />
            <DictionaryModal
                isOpen={isModalOpen}
                onClose={() => setIsModalOpen(false)}
                title={selectedRules ? 'Редактировать' : 'Добавить'}
                selected={selectedRules}
                onSave={handleSaveRules}
            />
        </div>
    );
}

export default RulesAdmins;