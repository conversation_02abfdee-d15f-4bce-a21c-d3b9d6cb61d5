import React, {useEffect, useState} from 'react';
import {useDispatch, useSelector} from "react-redux";
import {
    activateUser,
    deactivateUser,
    deleteUser,
    getAllUsers
} from "../../../../store/actions/admin/adminListAction.js";
import Table from "../../../../shared/ui/table/Table.jsx";


function UserList() {
    const dispatch = useDispatch()
    const {Data, Count} = useSelector(state => state.admin?.users)
    const [page, setPage] = useState(1);
    const limit = 10;


    useEffect(() => {
        dispatch(getAllUsers({ page: page, limit: limit }))
    }, [dispatch, page]);

    const handleEditKey = (row) => {
        console.log('Edit:', row);
    };

    const deleteKey = (row) => {
        dispatch(deleteUser(row.ID)).then(() => {
            dispatch(getAllUsers({ page, limit }));
        });
    };


    const columns = [
        { field: 'ID', headerName: 'ID', width: '5%' },
        { field: 'Profile.Name', headerName: 'Имя', width: '10%' },
        { field: 'Profile.PhoneNumber', headerName: 'Номер телефона', width: '15%' },
        { field: 'Email', headerName: 'Почта', width: '20%' },
        { field: 'Role', headerName: 'Роль', width: '10%' },
        {
            field: 'status',
            headerName: 'Статус',
            cell: (row) => (
                <label className="relative inline-flex items-center cursor-pointer">
                    <input
                        type="checkbox"
                        checked={row.Active}
                        onChange={(e) => handleStatusChange(row, e.target.checked)}
                        className="sr-only peer"
                    />
                    <div
                        className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-0 peer-focus:ring-red-500 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-500"
                    ></div>
                    <span className="ml-3 text-sm font-semibold text-gray-900">
                    {row.Active ? 'Активен' : 'Отключен'}
                </span>
                </label>
            ),
            width: '10%'
        },
        {
            field: 'actions',
            headerName: '',
            width: '5%',
            handleEdit: handleEditKey,
            handleDelete: deleteKey
        }
    ];



    const handleStatusChange = (row, newStatus) => {
        // Выполняем запрос на сервер для активации/деактивации пользователя
        if (newStatus) {
            dispatch(activateUser(row.ID)).then(() => {
                // После успешной активации, обновляем список пользователей
                dispatch(getAllUsers({ page, limit }));
            });
        } else {
            dispatch(deactivateUser(row.ID)).then(() => {
                // После успешной деактивации, обновляем список пользователей
                dispatch(getAllUsers({ page, limit }));
            });
        }
    };




    const handlePageChange = (newPage) => {
        setPage(newPage);
    };

    return (
        <div>
            <h1 className='font-semibold text-xl'>Список пользователей</h1>
            <Table
                columns={columns}
                data={Data}
                handleEdit={handleEditKey}
                handleDelete={deleteKey}
                totalItems={Count}
                itemsPerPage={limit}
                currentPage={page}
                onPageChange={handlePageChange}
            />
        </div>
    );
}

export default UserList;