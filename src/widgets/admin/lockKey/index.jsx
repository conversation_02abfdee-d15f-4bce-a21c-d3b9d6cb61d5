import React, {useEffect, useState} from 'react';
import {useDispatch, useSelector} from "react-redux";
import {useNavigate} from "react-router-dom";
import {getAllAccomodations} from "../../../store/actions/admin/adminListAction.js";
import Table from "../../../shared/ui/table/Table.jsx";
import KeyLockModal from "../../../features/admin/modal/index.jsx";
import {getTTLocks, createTTLocks, deleteTTLock} from "../../../store/actions/lockKey/lockKey.js";


function LockKeys(props) {
    const dispatch = useDispatch()
    const {Data, Count} = useSelector(state => state.admin?.accommodations)
    const ttlock = useSelector(state => state.lockKeys?.ttlocks);

    const navigate = useNavigate()
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 10;
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [selectedAccommodationId, setSelectedAccommodationId] = useState(null);

    useEffect(() => {
        dispatch(getAllAccomodations({ page: currentPage, limit: itemsPerPage }))
        dispatch(getTTLocks())
    }, [dispatch, currentPage]);

    const openModal = (id) => {
        setSelectedAccommodationId(id);
        setIsModalOpen(true)
    }

    const handleBindLock = (selectedLock) => {
        const newKey = {
            Username: selectedAccommodationId.Owner.Email,
            AccommodationId: selectedAccommodationId.ID,
            LockName: selectedLock.LockName,
            Date: Date.now().toString(),
        };

        dispatch(createTTLocks(newKey))
            .unwrap()
            .then(() => {
                dispatch(getAllAccomodations({ page: currentPage, limit: itemsPerPage }));
            })
            .catch(err => console.error('Ошибка при добавлении замка:', err))
            .finally(() => {
                setIsModalOpen(false);
                setSelectedAccommodationId(null);
            });
    };

    const columns = [
        { field: 'ID', headerName: 'ID', width: '5%' },
        { field: 'Title', headerName: 'Название', width: '60%' },
        {
            field: 'Owner.Nickname',
            headerName: 'Владелец',
            cell: row => row.Owner?.Profile?.Nickname || 'Нет данных',
            width: '15%'
        },
        {
            field: 'Owner.PhoneNumber',
            headerName: 'Телефон',
            cell: row => row.Owner?.Profile?.PhoneNumber || 'Нет данных',
            width: '15%'
        },
        {
            field: 'Locks',
            headerName: 'Замок',
            cell: (row) => {
                if (row.Locks && row.Locks.length > 0) {
                    return (
                        <div className="flex flex-col gap-1">
                            {row.Locks.map(lock => (
                                <div key={lock.ID} className="flex items-center gap-2">
                                    <span className="text-sm">{lock.LockName}</span>
                                    <button
                                        className="text-red-500 w-[18px] h-[18px] flex items-center justify-center hover:text-red-700"
                                        onClick={() => handleDeleteTTLock(lock.ID)}
                                    >
                                        <img className="w-[16px] h-[16px]" alt="delete" src="/deleteIcon.svg" />
                                    </button>
                                </div>
                            ))}
                        </div>
                    );
                }
                return (
                    <button
                        className="bg-gray-300 rounded-md w-full hover:bg-black hover:text-white flex items-center justify-center px-2 py-1"
                        onClick={() => openModal(row)}
                    >
                        <span className="text-lg">+</span>
                    </button>
                );
            },
            width: '10%'
        }
    ];


    const handleDeleteTTLock = (lockId) => {
        dispatch(deleteTTLock(lockId))
            .unwrap()
            .then(() => {
                dispatch(getAllAccomodations({ page: currentPage, limit: itemsPerPage }));
            })
            .catch((error) => {
                console.error('Ошибка удаления замка', error);
            });
    };

    const handlePageChange = (newPage) => {
        setCurrentPage(newPage);
    };

    return (
        <div>
            <h1 className='font-semibold text-xl'>Замки</h1>
            <Table
                columns={columns}
                data={Data}
                totalItems={Count}
                handleDelete={handleDeleteTTLock}
                itemsPerPage={itemsPerPage}
                currentPage={currentPage}
                onPageChange={handlePageChange}
                editButtons={false}
                deleteButtons={false}
            />
            <KeyLockModal
                data={ttlock}
                isOpen={isModalOpen}
                onClose={() => setIsModalOpen(false)}
                crossClose={true}
                title={'Привязать к замку'}
                buttonHidde={false}
                onConfirm={handleBindLock}
                style={'w-1/3'}
                />
        </div>
    );
}

export default LockKeys;