import React from 'react';
import { NavLink, useLocation } from "react-router-dom";
import { FiUser } from "react-icons/fi";
import { BsHouse, BsCalendar3 } from "react-icons/bs";
import { IoLockOpenOutline } from "react-icons/io5";
import { RiListCheck3 } from "react-icons/ri";
import { TbCategory } from "react-icons/tb";
import { IoSettingsOutline } from "react-icons/io5";
import { MdOutlineAnnouncement } from "react-icons/md";


export const menuItems = [
    {
        name: 'Управление',
        children: [
            { name: 'Объявления', path: '/admin/accommodation',  icon: <img src="/moderator/announcement.svg" alt='img' className="w-7 h-7" /> },
            { name: 'Пользователи', path: '/admin/users', icon: <img src="/moderator/usersM.svg" alt='img' className="w-8 h-8" /> },
            { name: 'Бронирования', path: '/admin/bookings', icon: <img src="/moderator/booking.svg" alt='img' className="w-8 h-8" />},
            { name: 'Замки и ключи', path: '/admin/locks', icon: <img src="/moderator/tLock.svg" alt='img' className="w-7 h-7" /> },
            { name: 'Заявки', path: '/admin/applications', icon: <img src="/moderator/incom.svg" alt='img' className="w-7 h-7" /> },
        ]
    },
    {
        name: '',
        children: [
            { name: 'Правила', path: '/admin/dictionary/rules', icon: <img src="/moderator/rules.svg" alt='img' className="w-8 h-8" /> },
            { name: 'Условия', path: '/admin/dictionary/facility', icon: <img src="/moderator/applicM.svg" alt='img' className="w-7 h-7" /> },
            { name: 'Категории', path: '/admin/dictionary/categories', icon: <img src="/moderator/category.svg" alt='img' className="w-8 h-8" /> }
        ]
    },
    {
        name: '',
        children: [
            {name: 'Настройки', path: '/admin/settings', icon: <img src="/moderator/settings.svg" alt='img' className="w-6.5 h-6.5" />}
        ]
    }
];

function SideBar() {

    return (
        <div className="w-64 h-full py-4 px-4 border-r overflow-y-auto">
            <ul className="">
                {menuItems.map((item, index) => (
                    <li key={index}>
                        {item.children ? (
                            <div className='border-b py-4'>
                                {item.name && (
                                    <p className="pb-3 pl-2 text-gray-900 font-semibold">
                                        {item.name}
                                    </p>
                                )}
                                <ul className='space-y-3'>
                                    {item.children.map((subItem, subIndex) =>
                                        <li key={subIndex}>
                                            <NavLink
                                                to={subItem.path}
                                                className={({isActive}) =>
                                                    `flex items-center gap-2 p-2 cursor-pointer font-medium hover:bg-gray-300 rounded-md  ${
                                                        isActive ? 'bg-gray-300' : 'text-gray-900'
                                                    }`
                                                }
                                            >
                                                <span className="flex items-center justify-center w-8 h-8">
                                                    {subItem.icon}
                                                </span>
                                                <span>{subItem.name}</span>
                                            </NavLink>
                                        </li>
                                    )}
                                </ul>
                            </div>
                        ) : (
                            <NavLink
                                to={item.path}
                                className={({isActive}) =>
                                    `block px-10 py-2 font-semibold cursor-pointer hover:bg-gray-700 hover:text-white ${
                                        isActive ? 'bg-gray-700 text-white' : 'text-gray-900'
                                    }`
                                }
                            >
                                {item.name}
                            </NavLink>
                        )}
                    </li>
                ))}
            </ul>
        </div>
    );
}

export default SideBar;
