import React, {useEffect, useState} from 'react';
import {useDispatch, useSelector} from "react-redux";
import Table from "../../../shared/ui/table/Table.jsx";
import {adminActions} from "../../../store/slices/admin/adminEntitySlice.js";
import DictionaryModal from "../../../features/admin/dictionary/CategoryModal.jsx";
import Button from "../../../shared/ui/button/Button.jsx";
import facilities from "../../../features/home/<USER>/facilities/index.jsx";

function FacilityAdmins() {
    const dispatch = useDispatch();
    const { data: facilities, status, error } = useSelector(
        state => state.adminEntities.facilities
    );
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [selectedFacility, setSelectedFacility] = useState(null);

    const [page, setPage] = useState(1);
    const limit = 10;


    useEffect(() => {
        dispatch(adminActions.fetchFacilities({ page, limit }));
    }, [dispatch, page, limit]);

    const handleEditKey = (row) => {
        setSelectedFacility(row)
        setIsModalOpen(true);
    };

    const handleAddCategory = () => {
        setSelectedFacility(null);
        setIsModalOpen(true);
    }

    const handleSaveCategory = (facility) => {
        if (selectedFacility) {
            const updatedCategory = {
                valueRu: facility.ru,
                valueKg: facility.ky,
                valueEn: facility.en,
                icon: facility.icon,
            };
            dispatch(adminActions.updateFacility({ id: selectedFacility.Id, updatedData: updatedCategory }))
                .then(() => dispatch(adminActions.fetchFacilities({ page, limit })));
        } else {
            const formattedCategory = {
                valueRu: facility.ru,
                valueKg: facility.ky,
                valueEn: facility.en,
                icon: facility.icon,
            };
            dispatch(adminActions.createFacility(formattedCategory))
                .then(() => dispatch(adminActions.fetchFacilities({ page, limit })));
        }
        setIsModalOpen(false);
    };

    const deleteKey = (row) => {
        dispatch(adminActions.deleteFacility(row.Id))
            .then(() => dispatch(adminActions.fetchFacilities({ page, limit })));
    };

    const columns = [
        { field: 'Id', headerName: 'ID', width: '5%' },
        { field: 'Value.ru', headerName: 'Русский', width: '15%' },
        { field: 'Value.ky', headerName: 'Кыргызский', width: '15%' },
        { field: 'Value.en', headerName: 'Английский', width: '15%' },
        { field: 'Icon', headerName: 'Иконки', width: '15%' },
        {
            field: 'actions',
            headerName: '',
            width: '5%',
            handleEdit: handleEditKey,
            handleDelete: deleteKey
        }
    ];

    const handlePageChange = (newPage) => {
        setPage(newPage);
    };

    return (
        <div>
            <div className='flex justify-between'>
                <h1 className='font-semibold text-xl'>Список условий</h1>
                <Button style='bg-blue-500 text-white' name='Добавить' onClick={handleAddCategory}/>
            </div>
            <Table
                columns={columns}
                data={facilities.Data}
                handleEdit={handleEditKey}
                handleDelete={deleteKey}
                totalItems={facilities.Count}
                itemsPerPage={limit}
                currentPage={page}
                onPageChange={handlePageChange}
            />
            <DictionaryModal
                isOpen={isModalOpen}
                onClose={() => setIsModalOpen(false)}
                title={selectedFacility ? 'Редактировать' : 'Добавить'}
                selected={selectedFacility}
                onSave={handleSaveCategory}
            />
        </div>
    );
}

export default FacilityAdmins;