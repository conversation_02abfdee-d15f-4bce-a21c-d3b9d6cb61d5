import React, {useEffect, useState} from 'react';
import {useDispatch, useSelector} from "react-redux";
import Table from "../../../../shared/ui/table/Table.jsx";
import {useNavigate} from "react-router-dom";
import {
    activateMyAccommodations, deactivateMyAccommodations,
    getAccommodations,
    removeMyAccommodations
} from "../../../../store/actions/accommodations/Accommodation.ts";
import {getAllAccomodations} from "../../../../store/actions/admin/adminListAction.js";
import {getUserInfo} from "../../../../store/actions/auth/Auth.js";

function AccommodationListAdmin() {
    const dispatch = useDispatch()
    const {Data, Count} = useSelector(state => state.admin?.accommodations)
    const navigate = useNavigate()
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 10;


    useEffect(() => {
        dispatch(getAllAccomodations({ page: currentPage, limit: itemsPerPage }))
    }, [dispatch, currentPage]);

    const handleEditKey = (row) => {
        navigate(`/owner/edit-accommodation/${row.ID}`)
    };

    const deleteKey = (row) => {
        dispatch(removeMyAccommodations(row.ID))

    };



    const columns = [
        { field: 'ID', headerName: 'ID', width: '5%' },
        { field: 'Title', headerName: 'Название', width: '20%' },
        {
            field: 'Owner.Nickname',
            headerName: 'Владелец',
            cell: row => row.Owner?.Profile?.Nickname || 'Нет данных',
            width: '10%'
        },
        {
            field: 'Owner.PhoneNumber',
            headerName: 'Телефон',
            cell: row => row.Owner?.Profile?.PhoneNumber || 'Нет данных',
            width: '10%'
        },
        { field: 'LocationLabel', headerName: 'Локация', width: '15%' },
        {
            field: 'status',
            headerName: 'Статус',
            cell: row => (
                <label className="relative inline-flex items-center cursor-pointer">
                    <input
                        type="checkbox"
                        checked={row.IsAvailable}
                        onChange={(e) => handleStatusChange(row, e.target.checked)}
                        className="sr-only peer"
                    />
                    <div
                        className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-0 peer-focus:ring-red-500 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-500"
                    ></div>
                    <span className="ml-3 text-sm font-semibold text-gray-900">
                    {row.IsAvailable ? 'Активен' : 'Отключен'}
                </span>
                </label>
            ),
            width: '10%'
        },
        {
            field: 'actions',
            headerName: '',
            width: '5%',
            handleEdit: handleEditKey,
            handleDelete: deleteKey
        }
    ];




    const handlePageChange = (newPage) => {
        setCurrentPage(newPage);
    };

    const handleStatusChange = (row, newStatus) => {
        if (newStatus) {
            dispatch(activateMyAccommodations({
                id: row.ID,
                updateCallback: () => dispatch(getAllAccomodations({ page: currentPage, limit: itemsPerPage }))
            }));
        } else {
            dispatch(deactivateMyAccommodations({
                id: row.ID,
                updateCallback: () => dispatch(getAllAccomodations({ page: currentPage, limit: itemsPerPage }))
            }));
        }
    };

    return (
        <div>
            <h1 className='font-semibold text-xl'>Список объявлении</h1>
            <Table
                columns={columns}
                data={Data}
                handleEdit={handleEditKey}
                handleDelete={deleteKey}
                totalItems={Count}
                itemsPerPage={itemsPerPage}
                currentPage={currentPage}
                onPageChange={handlePageChange}
            />
        </div>
    );
}

export default AccommodationListAdmin;