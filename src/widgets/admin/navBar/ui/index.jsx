import React from 'react';
import {Link} from "react-router-dom";
import Notification from "../../../../features/notification/ui/Notification.jsx";

function NavBar(props) {
    return (
        <nav className="border-b px-20 py-4 flex justify-between items-center">
            <img className='w-[100px]' src='/logoTLock.svg' alt='img'/>
            <div className='flex items-center gap-2'>
                <Notification/>
                <div className="space-x-4">
                    <Link to='/'>
                        Вернутся
                    </Link>
                </div>
            </div>
        </nav>
    );
}

export default NavBar;