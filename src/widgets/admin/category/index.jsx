import React, {useEffect, useState} from 'react';
import {useDispatch, useSelector} from "react-redux";
import Table from "../../../shared/ui/table/Table.jsx";
import {adminActions} from "../../../store/slices/admin/adminEntitySlice.js";
import DictionaryModal from "../../../features/admin/dictionary/CategoryModal.jsx";
import Button from "../../../shared/ui/button/Button.jsx";

function CategoryAdmins() {
    const dispatch = useDispatch();
    const { data: categories, status, error } = useSelector(
        state => state.adminEntities.categories
    );
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [selectedCategory, setSelectedCategory] = useState(null);

    const [page, setPage] = useState(1);
    const limit = 10;


    useEffect(() => {
        dispatch(adminActions.fetchCategories({ page, limit }));
    }, [dispatch, page, limit]);

    const handleEditKey = (row) => {
        setSelectedCategory(row)
        setIsModalOpen(true);
    };

    const handleAddCategory = () => {
        setSelectedCategory(null);
        setIsModalOpen(true);
    }

    const handleSaveCategory = (category) => {
        if (selectedCategory) {
            const updatedCategory = {
                nameRu: category.ru,
                nameKg: category.kg,
                nameEn: category.en,
                icon: category.icon,
            };
            dispatch(adminActions.updateCategory({ id: selectedCategory.Id, updatedData: updatedCategory }))
                .then(() => dispatch(adminActions.fetchCategories({ page, limit })));
        } else {
            const formattedCategory = {
                NameRu: category.ru,
                NameKg: category.kg,
                NameEn: category.en,
                Icon: category.icon,
            };
            dispatch(adminActions.createCategory(formattedCategory))
                .then(() => dispatch(adminActions.fetchCategories({ page, limit })));
        }
        setIsModalOpen(false);
    };

    const deleteKey = (row) => {
        dispatch(adminActions.deleteCategory(row.Id))
            .then(() => dispatch(adminActions.fetchCategories({ page, limit })));
    };

    const columns = [
        { field: 'Id', headerName: 'ID', width: '5%' },
        { field: 'Name.ru', headerName: 'Русский', width: '15%' },
        { field: 'Name.kg', headerName: 'Кыргызский', width: '15%' },
        { field: 'Name.en', headerName: 'Английский', width: '15%' },
        { field: 'Icon', headerName: 'Иконки', width: '15%' },
        {
            field: 'actions',
            headerName: '',
            width: '5%',
            handleEdit: handleEditKey,
            handleDelete: deleteKey
        }
    ];

    const handlePageChange = (newPage) => {
        setPage(newPage);
    };


    return (
        <div>
            <div className='flex justify-between'>
                <h1 className='font-semibold text-xl'>Список категории</h1>
                <Button style='bg-blue-500 text-white' name='Добавить' onClick={handleAddCategory}/>
            </div>
            <Table
                columns={columns}
                data={categories.Data}
                handleEdit={handleEditKey}
                handleDelete={deleteKey}
                totalItems={categories.Count}
                itemsPerPage={limit}
                currentPage={page}
                onPageChange={handlePageChange}
            />
            <DictionaryModal
                isOpen={isModalOpen}
                onClose={() => setIsModalOpen(false)}
                title={selectedCategory ? 'Редактировать категорию' : 'Добавить категорию'}
                selected={selectedCategory}
                onSave={handleSaveCategory}
            />
        </div>
    );
}

export default CategoryAdmins;