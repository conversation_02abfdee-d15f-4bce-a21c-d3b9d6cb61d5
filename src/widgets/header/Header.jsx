import {Link, useLocation, useNavigate} from 'react-router-dom';
import UserProfileMenu from "../userDropdownProfile/index.jsx";
import Notification from "../../features/notification/ui/Notification.jsx";
import { useIsAuthenticated } from 'store/selectors/AuthSelectors.js';
import Search from "../../features/home/<USER>/ui/index.jsx";
import {useDispatch, useSelector} from "react-redux";
import {useEffect, useState} from "react";
import Modal from "../../shared/ui/modal/Modal.jsx";
import LanguageSwitcher from "../../shared/ui/lang/index.jsx";
import { useTranslation } from 'react-i18next';
import {selectIsModerator, selectIsOwner} from "../../store/selectors/authRole.js";
import {fetchProfile} from "../../store/actions/auth/Auth.js";
import {FaPhone} from "react-icons/fa";
import { CiPhone } from "react-icons/ci";


function Header() {
    const isAuthenticated = useIsAuthenticated();
    const isModerator = useSelector(selectIsModerator)
    const isOwner = useSelector(selectIsOwner)
    const userStatus = useSelector(state => state.auth?.user?.user?.status || "");
    const status = useSelector(state => state.auth?.userStatus || "");
    const navigate = useNavigate()
    const location = useLocation();
    const isProfilePage = location.pathname === '/profile';
    const hideSearchOnPaths = ['/profile', '/profile/settings', '/profile/personal-info, /profile/my-bookings'];
    const showSearch = !hideSearchOnPaths.includes(location.pathname);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const { t } = useTranslation();
    const main = location.pathname === '/';
    const dispatch = useDispatch();


    const handleConfirm = () => {
        setIsModalOpen(false);
        navigate('/profile/add-passport');
    };

    const handleModalClose = () => {
        setIsModalOpen(false);
    };


    return (
        <div className='container mx-auto p-3 md:px-5 lg:px-5 xl:px-0 border-b border-gray-400'>
            {isModerator && main && (
                <div className='flex justify-center py-20 mb-10 bg-[url("/code.jpg")]'>
                    <div className='flex flex-col items-center bg-white gap-5  p-10'>
                        <p className='font-semibold text-xl'>Добро пожаловать Модератор!</p>
                        <Link to='/admin'>
                            <p className='px-2 py-1 border w-max cursor-pointer bg-white font-semibold rounded'>Управление контетном</p>
                        </Link>
                    </div>
                </div>
            )}
            <div className='flex w-full justify-between items-center'>
                <div className='flex items-center'>
                <Link to='/' className='font-bold p-2 cursor-pointer rounded-md'>
                    <img className='w-[100px]' src='/logoTLock.svg' alt='img'/>
                </Link>
                </div>
                {
                    showSearch  && (
                        <div className='hidden md:block w-1/2'>
                            <Search/>
                        </div>
                    )
                }
                <div className='flex items-center md:space-x-4'>
                    <a
                        href="tel:+996704314314"
                        className="hidden lg:flex items-center gap-1 hover:text-blue-600"
                    >
                        <CiPhone size={20} />
                        +996 704 314 314
                    </a>
                    <LanguageSwitcher/>
                    {isAuthenticated ? (
                        <div className='flex items-center gap-4'>
                            {(status === "approved" || status === "pending") ? (
                                <>

                                    <Link to='owner' className=''>
                                        <img src='/objectIcon.svg' alt='img'/>
                                    </Link>
                                </>
                            ) : (
                                <p className='font-semibold cursor-pointer text-base'>
                                    <span
                                        onClick={status === "" ? () => setIsModalOpen(true) : undefined}
                                    >{t('header.rentHouse')}</span>
                                </p>
                            )}
                            <Notification/>
                            <UserProfileMenu/>
                        </div>
                    ) : (
                        <>
                                <div>
                                    <Link to='/auth/login' className='flex items-center gap-2'>
                                        <img src='/enterIcon.svg' alt='img'/>
                                        <p className='font-medium text-lg'>{t('header.login')}</p>
                                    </Link>
                                </div>
                            </>
                    )}
                </div>

            </div>
            <p className="flex justify-end text-sm lg:hidden text-right  text-blue-600">
                <CiPhone size={20} />
                <a href="tel:+996700123456" className=" font-semibold">
                    +996 704 314 314
                </a>
            </p>
            {
                showSearch  && (
                    <div className='block md:hidden mt-5'>
                        <Search/>
                    </div>
                )
            }
            <Modal
                isOpen={isModalOpen}
                onClose={handleModalClose}
                onConfirm={handleConfirm}
                title={t('profilePage.FillPassportDetails')}
                message={t('profilePage.message')}
                confirmText={t('button.fillIn')}
                cancelText={t('button.cancel')}
                confirmButtonProps={{
                    className: 'w-full px-4 py-2 bg-blue-500 text-white rounded-md cursor-pointer'
                }}
            />
        </div>
    );
}

export default Header;
