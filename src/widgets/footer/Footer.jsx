import { <PERSON><PERSON><PERSON><PERSON>, FaInstagram, FaTelegram } from "react-icons/fa";
import { FaFacebook, FaXTwitter, FaWhatsapp } from "react-icons/fa6";
import { CiMail } from "react-icons/ci";
import { RiTiktokFill } from "react-icons/ri";
import { LuMail } from "react-icons/lu";
import { SlPhone } from "react-icons/sl";
import {Link} from "react-router-dom";
import {useTranslation} from "react-i18next";

const socialIcons = [
    // { icon: FaFacebook, link: "https://www.facebook.com" },
    // { icon: FaTelegram, link: "https://www.telegram.com" },
    { icon: FaInstagram, link: "https://www.instagram.com/togolock?igsh=MWJhM3BlM3o3NWgwZQ==" },
    // { icon: FaXTwitter, link: "https://www.twitter.com" },
    { icon: <PERSON>a<PERSON><PERSON><PERSON><PERSON>, link: "https://wa.me/996704314314" },
    { icon: Ri<PERSON><PERSON><PERSON><PERSON>ill, link: "https://www.tiktok.com/@togolock.kg" },
    { icon: LuMail, link: "mailto:<EMAIL>" },

];

const storeLinks = [
    {
        name: 'Google Play',
        link: 'https://play.google.com/store/apps/details?id=com.joneleumka.togolock',
        img: '/googlePlay.svg', // сохрани это изображение в `public/`
        alt: 'Get it on Google Play',
    },
    {
        name: 'App Store',
        link: 'https://apps.apple.com/kg/app/togolock/id6742218319',
        img: '/appStore.svg', // тоже сохранить в `public/`
        alt: 'Download on the App Store',
    },
];

function Footer() {
    const {t} = useTranslation()
    return (
        <footer className='bg-gray-200 text-light-text w-full'>
            <div className='container mx-auto flex flex-col items-center gap-5 justify-between w-full py-5 md:py-10'>
                <Link to='/' className='font-bold p-2 cursor-pointer rounded-md'>
                    <img className='w-[100px]' src='/logoTLock.svg' alt='img'/>
                </Link>
                <nav className='flex gap-5'>
                    <Link to='/profile' className='border-b hover:border-b hover:border-amber-300'>{t('footer.profile')}</Link>
                    <Link to='/about' className='border-b hover:border-b hover:border-amber-300'>{t('footer.aboutUs')}</Link>
                    <Link to='/profile/settings' className='border-b hover:border-b hover:border-amber-300'>{t('footer.settings')}</Link>
                </nav>
                <div className="flex gap-4 mt-2">
                    {storeLinks.map((store, index) => (
                        <a
                            key={index}
                            href={store.link}
                            target="_blank"
                            rel="noopener noreferrer"
                        >
                            <img
                                src={store.img}
                                alt={store.alt}
                                className="w-[120px] h-[40px] object-cover"
                            />
                        </a>
                    ))}
                </div>
            </div>
            <div className='bg-black'>
                <div className='container mx-auto flex flex-col-reverse md:flex-row gap-4 justify-between items-center text-white py-5'>
                    <p className='text-sm'>© {new Date().getFullYear()} {t('footer.allRightsReserved')}.</p>
                    <div className='flex gap-5'>
                        <a
                            href="tel:+996704314314"
                            className="flex items-center gap-2 hover:text-amber-300"
                        >
                            <SlPhone  size={20} />
                            +996 704 314 314
                        </a>
                        {socialIcons.map((item, index) => (
                            <a key={index} href={item.link} target="_blank" rel="noopener noreferrer"
                               className='cursor-pointer hover:text-amber-300'>
                                <item.icon size={25}/>
                            </a>
                        ))}
                    </div>
                </div>
            </div>
        </footer>
    );
}

export default Footer;
