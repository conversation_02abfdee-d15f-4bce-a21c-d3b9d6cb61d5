import {useSelector} from "react-redux";
import {selectIsModerator} from "../../store/selectors/authRole.js";
import {Navigate} from "react-router-dom";
import React from "react";
import {useIsAuthenticated} from "../../store/selectors/AuthSelectors.js";

const AuthLayout = ({ children }) => {




    return <div className="flex flex-col min-h-screen justify-center items-center">
        <div className='flex-grow flex flex-col w-full lg:px-10 xl:px-20 py-5'>
            {children}
        </div>
    </div>
}

export default AuthLayout;