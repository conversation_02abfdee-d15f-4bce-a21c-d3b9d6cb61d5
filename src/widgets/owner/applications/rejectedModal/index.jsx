import React from 'react';
import Button from "../../../../shared/ui/button/Button.jsx";
import {useTranslation} from "react-i18next";

function RejectedModal({
   rejectionReason,
   setRejectionReason,
   handleRejectSubmit,
   handleModalClose
}){
    const {t} = useTranslation()
    return (
        <div className="fixed z-30 inset-0 bg-black bg-opacity-50 flex items-center justify-center">
            <div className="bg-white mx-10 md:m-0 p-4 sm:p-6 rounded shadow-lg w-full md:w-1/2">
                <div className='flex flex-col'>
                    <div className='flex flex-col gap-2'>
                        <label className='font-semibold'>{t('rejectedModal.reasonForRefusal')}</label>
                        <textarea
                            rows="4"
                            value={rejectionReason}
                            onChange={(e) => setRejectionReason(e.target.value)}
                            className='w-full border rounded-md focus:outline-none p-2'
                            required
                        ></textarea>
                    </div>
                    <div className='flex justify-between gap-5 mt-3'>
                        <Button style='bg-green-500 text-white' name={t('button.send')} onClick={handleRejectSubmit}/>
                        <Button style='' name={t('button.close')} onClick={handleModalClose}/>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default RejectedModal;