import Button from "../../../../shared/ui/button/Button.jsx";
import ApplicantsCard from "../../../../entities/applicantCard/ui/index.jsx";
import { useDispatch, useSelector } from "react-redux";
import React, { useEffect, useState } from "react";
import {activateBooking, getByAccommodation, rejectBooking} from "../../../../store/actions/booking/Booking.js";
import RejectedModal from "../rejectedModal/index.jsx";
import {useTranslation} from "react-i18next";

const Applications = () => {
    const dispatch = useDispatch();
    const {t} = useTranslation()
    const applications = useSelector(state => state.booking?.accommodationsBooking);

    const [showModal, setShowModal] = useState(false);
    const [selectedBookingId, setSelectedBookingId] = useState(null);
    const [rejectionReason, setRejectionReason] = useState('');
    const [activeTab, setActiveTab] = useState('pending');
    const [loadingConfirm, setLoadingConfirm] = useState({});
    const [loadingReject, setLoadingReject] = useState({});


    useEffect(() => {
        dispatch(getByAccommodation({ status: activeTab, limit: 50, page: 1}));
    }, [dispatch, activeTab, rejectionReason]);


    const clickStatus = (status) => {
        setActiveTab(status); // Изменяем активный статус
    };

    const handleConfirm = async (id) => {
        setLoadingConfirm(prev => ({ ...prev, [id]: true }));
        await dispatch(activateBooking(id));
        setLoadingConfirm(prev => ({ ...prev, [id]: false }));
        dispatch(getByAccommodation({ status: activeTab, limit: 20, page: 1 }));
    };

    const handleReject = (id) => {
        setSelectedBookingId(id);
        setShowModal(true);
    };

    const handleModalClose = () => {
        setShowModal(false);
        setSelectedBookingId(null);
    };

    const handleRejectSubmit = async () => {
        if (selectedBookingId) {
            setLoadingReject(prev => ({ ...prev, [selectedBookingId]: true })); // Start loading
            await dispatch(rejectBooking({ id: selectedBookingId, RejectionReason: rejectionReason }));
            setLoadingReject(prev => ({ ...prev, [selectedBookingId]: false })); // Stop loading
            setShowModal(false);
            setSelectedBookingId(null);
            setRejectionReason('');
            dispatch(getByAccommodation({ status: activeTab, limit: 20, page: 1 }));
        }
    };

    const tabs = [
        { name: 'applications.inbox', status: 'pending' },
        { name: 'applications.inProgress', status: 'active' },
        { name: 'applications.rejected', status: 'rejected' }
    ];


    return (
        <div className='w-full px-2'>
            <h2 className='text-2xl font-bold mb-4 text-blue-text-lock'>{t('ownerPage.applications')}</h2>
            <div className='flex space-x-2 md:space-x-4 mb-4'>
                {tabs.map(tab => (
                    <Button
                        key={tab.status}
                        name={t(tab.name)}
                        onClick={() => clickStatus(tab.status)}
                        style={`${activeTab === tab.status ? ' border-blue-600' : ''} text-sm md:text-base rounded-none `}
                    />
                ))}
            </div>
            <div>

            </div>
            {applications.length > 0 ? (

            <div className='grid md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3  gap-5'>
                {
                    [...applications].reverse().map(item => {
                        return(
                            <ApplicantsCard
                                key={item.ID}
                                item={item}
                                onConfirm={handleConfirm}
                                onReject={handleReject}
                                loadingConfirm={loadingConfirm}
                                loadingReject={loadingReject}
                            />
                        )
                    })
                }
            </div>
                ) : (
                    <div className='flex justify-center items-center'>
                        <p className="text-xl font-semibold">{t('applications.noApplications')}</p>
                    </div>
                )}
            {showModal && (
               <RejectedModal
                   rejectionReason={rejectionReason}
                   setRejectionReason={setRejectionReason}
                   handleRejectSubmit={handleRejectSubmit}
                   handleModalClose={handleModalClose}

               />
            )}
        </div>
    );
};

export default Applications;



