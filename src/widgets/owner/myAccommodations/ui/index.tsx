import RoomsCard from "entities/roomCard/ui/index.jsx";
import React, {useEffect, useState} from "react";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch } from "app/store";
import { activateMyAccommodations, deactivateMyAccommodations, getMyAccommodations, removeMyAccommodations } from "store/actions/accommodations/Accommodation";
import { useNavigate } from "react-router-dom";
import {useTranslation} from "react-i18next";

const MyAccommodations: React.FC = () => {
    const dispatch = useDispatch<AppDispatch>();
    const myAccommodations = useSelector((state: any) => state.accommodations?.myAccommodations || []); // Ensure myAccommodations is not null
    const userRole = useSelector((state: any) => state.auth.user.user.Role);
    const {t} = useTranslation()

    useEffect(() => {
        dispatch(getMyAccommodations());
    }, [dispatch]);

    const navigate = useNavigate();

    const onArgs = (Accommodation: { ID: number; IsAvailable?: boolean }) => ({
        onRemove: () => {
            dispatch(removeMyAccommodations(Accommodation.ID));
        },
        onPublish: () => {
            Accommodation?.IsAvailable
                ? dispatch(deactivateMyAccommodations({
                    id: Accommodation.ID,
                    updateCallback: () => dispatch(getMyAccommodations())
                }))
                : dispatch(activateMyAccommodations({
                    id: Accommodation.ID,
                    updateCallback: () => dispatch(getMyAccommodations())
                }));
        },
        onEdit: () => {
            navigate("/owner/edit-accommodation/" + Accommodation.ID);
        },
        room: Accommodation,
    });


    const sortedAccommodations = [...myAccommodations].sort((a, b) => a.Accommodation.ID - b.Accommodation.ID);


    return (
        <div className='container mx-auto w-full md:pl-5'>
            <div className='flex justify-between mb-4'>
                <h2 className='text-2xl font-bold mb-4'>{t('ownerPage.yourAdvertisements')}</h2>
                <div>
                    <button
                        onClick={() => navigate('/owner/accommodation')}
                        className='font-medium px-4 py-2 border text-light-text hover:bg-lock-orange hover:text-white rounded h-max'
                    >
                        {t('ownerPage.add')}
                    </button>
                </div>
            </div>
            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 gap-5'>
                {myAccommodations.length > 0 ? (
                    sortedAccommodations.map(({Accommodation}: { Accommodation: any }) => (
                        <RoomsCard
                            key={Accommodation.ID}
                            {...onArgs(Accommodation)}
                        />
                    ))
                ) : (
                    <p>{t('ownerPage.noAccommodation')}</p>
                )}
            </div>
        </div>
    );
};

export default MyAccommodations;
