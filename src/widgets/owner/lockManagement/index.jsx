    import React, { useEffect, useState, useRef } from 'react';
import { useDispatch, useSelector } from "react-redux";
import { getMyAccommodations } from "store/actions/accommodations/Accommodation.ts";
import AccommodationSelector from "features/owner/calendar/accommodationSelector/index.jsx";
import Button from "../../../shared/ui/button/Button.jsx";
import {
    closedLock,
    createNewLockKey,
    deleteLockKey,
    getLocks,
    openLock,
    updateLockKey
} from "../../../store/actions/lockKey/lockKey.js";
import {formatDate, formatDateTime} from "../../../shared/utils/formatDate/index.js";
import { IoIosArrowUp, IoIosArrowDown } from 'react-icons/io';
import AddKey from "../../../features/owner/lockManagement/modal/AddKey.jsx";
import DeleteKey from "../../../features/owner/lockManagement/modal/DeleteKey.jsx";
import Spinner from "../../../shared/ui/spinner/Spinner.jsx";
    import {useTranslation} from "react-i18next";

function LockManagement(props) {
    const dispatch = useDispatch();
    const myAccommodation = useSelector(state => state.accommodations.myAccommodations);
    const lockKeys = useSelector(state => state.lockKeys.locks);
    const loadingLockKeys = useSelector(state => state.lockKeys.loading);
    const [selectedAccommodation, setSelectedAccommodation] = useState(null);
    const [expandedLock, setExpandedLock] = useState(null);
    const [currentKey, setCurrentKey] = useState(null);
    const [selectDeleteKey, setSelectDeleteKey] = useState(null);
    const [lockID, setLockID] = useState(null);
    const [modalOpen, setModalOpen] = useState(false);
    const [deleteModalOpen, setDeleteModalOpen] = useState(false);
    const loadingOpenLocks = useSelector(state => state.lockKeys.loadingOpenLocks);
    const loadingCloseLocks = useSelector(state => state.lockKeys.loadingCloseLocks);
    const {t} = useTranslation()

    useEffect(() => {
        dispatch(getMyAccommodations());
    }, [dispatch]);


    useEffect(() => {
        if (selectedAccommodation) {
            dispatch(getLocks(selectedAccommodation.Accommodation.ID));
        }
    }, [dispatch, selectedAccommodation]);

    const handleAccommodationSelect = (accommodation) => {
        setSelectedAccommodation(accommodation);
    };

    const toggleLockExpansion = (lockID) => {
        setExpandedLock(expandedLock === lockID ? null : lockID);
    };

    const handleSaveKey = (keyData) => {
        if (lockID) {
            keyData.LockID = lockID;
        }
        if (currentKey) {
            dispatch(updateLockKey({ id: currentKey.ID, values: keyData }))
                .then(() => {
                    dispatch(getLocks(selectedAccommodation.Accommodation.ID));
                    setModalOpen(false);
                });
        } else {
            dispatch(createNewLockKey(keyData))
                .then(() => {
                    dispatch(getLocks(selectedAccommodation.Accommodation.ID));
                    setModalOpen(false);
                });
        }
    };

    const handleEditKey = (key) => {
        setCurrentKey(key);
        setModalOpen(true);
    };


    const handleDeleteKey = (keyData) => {
        dispatch(deleteLockKey(keyData))
            .then(() => {
                dispatch(getLocks(selectedAccommodation.Accommodation.ID));
                setDeleteModalOpen(false);

            });
    };

    const deleteKey = (key) => {
        setSelectDeleteKey(key)
        setDeleteModalOpen(true);
    };


    const handleOpenLock = (lockID) => {
        dispatch(openLock(lockID));
    };

    const handleCloseLock = (lockID) => {
        dispatch(closedLock(lockID));
    };


    return (
        <div>
            <h2 className="text-2xl font-bold mb-4">{t('ownerPage.lockManagement')}</h2>
            <AccommodationSelector
                myAccommodation={myAccommodation}
                selectedAccommodation={selectedAccommodation}
                onSelect={handleAccommodationSelect}
            />
            <div className="mt-5">
                {!selectedAccommodation ? (
                    <h3 className="text-center text-xl font-medium">{t('lockManagement.chooseAccommodation')}</h3>
                ): (
                    lockKeys.length !== 0 ? lockKeys.map((item) => {
                            const isExpanded = expandedLock === item.ID;
                            return (
                                <React.Fragment key={item.ID}>
                                    <div className="flex flex-col gap-4 md:gap-0 md:flex-row justify-between items-center border rounded-md p-4 my-2 cursor-pointer"
                                         onClick={() => toggleLockExpansion(item.ID)}
                                    >
                                        <span className="font-semibold">{item.LockAlias}</span>
                                        <div className="flex justify-between items-center gap-3">
                                            <button className='rounded-md cursor-pointer border p-1.5'
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        setLockID(item.ID)
                                                        setModalOpen(true);
                                                        setCurrentKey(null);
                                                    }}
                                            >
                                                <img className='w-5 h-5' alt='img' src='/addPlus.svg'/>
                                            </button>
                                            <Button name={t('button.close')}
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        handleCloseLock(item.ID);
                                                    }}
                                                    style='border-text-light-red text-text-light-red font-semibold'
                                                    iconComponent={loadingCloseLocks[item.ID] ? <Spinner /> : <img src="/keyClose.svg" alt="Icon" className="w-4 h-4" />}
                                                    loading={loadingCloseLocks[item.ID]}
                                                    iconStyle='left-3'
                                            />
                                            <Button name={t('button.open')}
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        handleOpenLock(item.ID);
                                                    }}
                                                    style='border-btn-border-blue font-semibold'
                                                    iconComponent={loadingOpenLocks[item.ID] ? <Spinner /> : <img src="/keyOpen.svg" alt="Icon" className="w-4 h-4" />}
                                                    loading={loadingOpenLocks[item.ID]}
                                                    customStyle={{ backgroundColor: 'rgba(75, 93, 255, 0.2)' }}
                                                    iconStyle='left-3'

                                            />
                                            {item.Passcodes.length !== 0 ? (
                                                <span  className='cursor-pointer'>
                                                    {isExpanded ? <IoIosArrowUp size={20}/> : <IoIosArrowDown size={20}/>}
                                                </span>
                                            ) : (
                                                <span className='p-2.5'></span>
                                            )}
                                        </div>
                                    </div>
                                    {item?.Passcodes.length !== 0 && (
                                        <div
                                            className={`overflow-hidden transition-max-height duration-500 ease-in-out`}
                                            style={{
                                                maxHeight: isExpanded ? '1500px' : '0px',
                                            }}
                                        >
                                            <div className="py-4">
                                                <div className="hidden md:block">
                                                    <table className="min-w-full bg-white border-b mb-4">
                                                        <thead>
                                                        <tr className="w-full text-light-text text-left border-b">
                                                            <th className="px-4 py-2">{t('lockManagement.key')}</th>
                                                            <th className="px-4 py-2">{t('lockManagement.period')}</th>
                                                            <th className="px-4 py-2">{t('lockManagement.client')}</th>
                                                            <th className="px-4 py-2">{t('lockManagement.code')}</th>
                                                            <th className="px-4 py-2">{t('lockManagement.ownerCode')}</th>
                                                            <th className="px-4 py-2"></th>
                                                        </tr>
                                                        </thead>
                                                        <tbody>
                                                        {item?.Passcodes.map((keys) => {
                                                            return (
                                                                <tr className="border-b text-light-text font-medium "
                                                                    key={keys.ID}>
                                                                    <td className="px-4 py-3">{keys.KeyboardPwdName}</td>
                                                                    {keys.StartDate ? (
                                                                        <td className="px-4 py-3">
                                                                            c {formatDateTime(keys.StartDate)} до {formatDateTime(keys.EndDate)}
                                                                        </td>
                                                                    ) : (
                                                                        <td className="px-4 py-3">{t('addLock.constant')}</td>
                                                                    )}
                                                                    <td className="px-4 py-3">Тестовый тестович</td>
                                                                    <td className="px-4 py-3">{keys.KeyboardPwd}</td>
                                                                    <td className="px-4 py-3">{item.NoKeyPwd}</td>
                                                                    <td className="px-4 py-3 flex justify-center items-center text-center md:justify-end space-x-2">
                                                                        <button
                                                                            className="text-blue-500 w-[20px] h-[20px]"
                                                                            onClick={(e) => {
                                                                                e.stopPropagation();
                                                                                handleEditKey(keys);
                                                                            }}
                                                                        >
                                                                            <img className='w-[20px] h-[20px]' alt='img'
                                                                                 src='/editIcon.svg'/>
                                                                        </button>
                                                                        <button
                                                                            className="text-red-500 w-[20px] h-[20px]"
                                                                            onClick={(e) => {
                                                                                e.stopPropagation();
                                                                                deleteKey(keys);
                                                                            }}
                                                                        >
                                                                            <img className='w-[20px] h-[20px]' alt='img'
                                                                                 src='/deleteIcon.svg'/>
                                                                        </button>
                                                                    </td>
                                                                </tr>
                                                            );
                                                        })}
                                                        </tbody>
                                                    </table>
                                                </div>
                                                {/* Card layout for mobile view */}
                                                <div className="block md:hidden">
                                                    {item.Passcodes.map((keys) => (
                                                        <div key={keys.ID}
                                                             className="border rounded-3xl px-2 py-4 mb-4 shadow-sm"
                                                             style={{backgroundColor: 'rgba(75, 93, 255, 0.2)'}}
                                                        >
                                                            <h3 className="font-semibold text-center mb-2 text-xl">{keys.KeyboardPwdName}</h3>
                                                            <div
                                                                className='flex flex-col gap-2 bg-white rounded-3xl p-4'>
                                                                <p className='flex justify-between font-medium text-light-text'>
                                                                    <span>{t('lockManagement.client')}:</span>
                                                                    <span></span>
                                                                </p>

                                                                <p className='flex justify-between font-medium text-light-text'>
                                                                    <span>{t('lockManagement.start')}:</span>
                                                                    <span>{keys.StartDate ? `${formatDateTime(keys.StartDate)}` : t('addLock.constant')}</span>
                                                                </p>
                                                                <p className='flex justify-between font-medium text-light-text'>
                                                                    <span>{t('lockManagement.end')}:</span>
                                                                    <span>{keys.StartDate ? `${formatDateTime(keys.EndDate)}` : t('addLock.constant')}</span>
                                                                </p>

                                                                <p className='flex justify-between font-medium text-light-text'>
                                                                    <span>{t('lockManagement.code')}:</span>
                                                                    <span>{keys.KeyboardPwd}</span>
                                                                </p>
                                                                <p className='flex justify-between font-medium text-light-text'>
                                                                    <span>{t('lockManagement.ownerCode')}:</span>
                                                                    <span>{item.NoKeyPwd}</span>
                                                                </p>
                                                                <div className="flex justify-between space-x-2 mt-2">
                                                                    <button
                                                                        className="text-blue-500 flex gap-2 border border-blue-500 px-3 py-2 rounded-lg"
                                                                        onClick={(e) => {
                                                                            e.stopPropagation();
                                                                            handleEditKey(keys);
                                                                        }}
                                                                    >
                                                                        <img className="w-5 h-5" alt="Edit"
                                                                             src="/editIcon.svg"/>
                                                                        <span>{t('button.change')}</span>
                                                                    </button>
                                                                    <button
                                                                        className="text-red-500 flex gap-2 border border-text-light-red rounded-lg px-3 py-2"
                                                                        onClick={(e) => {
                                                                            e.stopPropagation();
                                                                            deleteKey(keys);
                                                                        }}
                                                                    >
                                                                        <img className="w-5 h-5" alt="Delete"
                                                                             src="/deleteIcon.svg"/>
                                                                        <span>{t('button.delete')}</span>

                                                                    </button>
                                                                </div>
                                                            </div>

                                                        </div>
                                                    ))}
                                                </div>
                                            </div>

                                        </div>
                                    )}

                                </React.Fragment>
                            );
                    }) : (
                        <h3 className='text-center text-2xl font-medium'>Нет замков привязанных к жилью</h3>
                    )
                )}

            </div>
            <AddKey
                isOpen={modalOpen}
                onClose={() => setModalOpen(false)}
                onSave={handleSaveKey}
                initialValues={currentKey || {}}
                loadingLockKeys={loadingLockKeys}
            />
            <DeleteKey
                isOpen={deleteModalOpen}
                onClose={() => setDeleteModalOpen(false)}
                onSave={handleDeleteKey}
                keyName={selectDeleteKey?.KeyboardPwdName}
                keyId={selectDeleteKey?.ID}
                loadingLockKeys={loadingLockKeys}
            />
        </div>
    );
}

    export default LockManagement;
