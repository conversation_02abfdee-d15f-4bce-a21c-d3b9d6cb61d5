import  { useEffect, useState } from 'react';
import {useDispatch, useSelector} from 'react-redux';
import { getMyAccommodations } from '../../../../store/actions/accommodations/Accommodation';
import AccommodationSelector from "../../../../features/owner/calendar/accommodationSelector/index.jsx";
import Calendar from "../../../../shared/utils/ calendar/index.jsx";
import {useTranslation} from "react-i18next";

const CalendarPage = () => {
    const dispatch = useDispatch();
    const myAccommodation = useSelector(state => state.accommodations.myAccommodations)
    const [selectedAccommodation, setSelectedAccommodation] = useState(null);
    const {t} = useTranslation()

    useEffect(() => {
        dispatch(getMyAccommodations());
    }, [dispatch]);

    const handleAccommodationSelect = (accommodation) => {
        setSelectedAccommodation(accommodation);
    };

    const selectedBookings = selectedAccommodation?.Calendar ?? [];


    return (
        <div className='container mx-auto w-full md:pl-5'>
            <h2 className="text-2xl font-bold mb-4">{t('ownerPage.calendar')}</h2>
            <AccommodationSelector
                myAccommodation={myAccommodation}
                selectedAccommodation={selectedAccommodation}
                onSelect={handleAccommodationSelect}
            />
            {
                !selectedAccommodation ? (
                    <div className='flex justify-center items-center'>
                        <p className='text-xl font-semibold'>{t('ownerPage.selectAccommodation')}</p>
                    </div>
                ) : selectedBookings.length === 0 ? (
                    <div className='flex justify-center items-center'>
                        <p className='text-xl font-semibold'>{t('ownerPage.noReservation')}</p>
                    </div>
                ) : (
                    <Calendar bookings={selectedBookings} />
                )
            }
        </div>
    );
};

export default CalendarPage;
