import {Link} from "react-router-dom";

function Owner() {

    const settingsLink = [
        { name: 'Управление объектами', description: 'Настройки и управление', path: '/profile/view-objects' },
        { name: 'Сдать жилье', description: 'Создать жилье', path: '/profile/view-objects' },
        { name: 'Заявки', description: 'Просмотр и управление заявками', path: '/profile/applications' },
        { name: 'Мое жилье', description: 'Просмотр и управление заявками', path: '/profile/my-objects' },
    ];

    return (
        <div className='sm:ml-5 lg:ml-10'>
            <h2 className='text-2xl font-bold mb-4'>Владелец</h2>
            <div className='grid grid-cols-1 lg:grid-cols-2 gap-4 md:w-max font-semibold w-full mx-auto'>
                {
                    settingsLink.map((item) => (
                        <Link key={item.name} to={item.path}
                              className='flex flex-col md:justify-center bg-white border rounded-lg p-4 cursor-pointer'>
                            <span>{item.name}</span>
                            <span className='text-sm text-gray-500'>{item.description}</span>
                        </Link>
                    ))
                }
            </div>
        </div>
    );
}

export default Owner;