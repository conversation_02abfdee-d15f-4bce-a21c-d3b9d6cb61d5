import { useEffect, useState } from 'react';
import { useForm, FormProvider } from 'react-hook-form';
import InputField from "shared/ui/inputField/InputField.jsx";
import { RiDeleteBinLine } from "react-icons/ri";
import ImagesInput from "shared/ui/inputField/ImagesInput.jsx";
import { handleImageUpload } from "shared/utils/imageLoadUtils.js";
import Button from "shared/ui/button/Button.jsx";
import {useDispatch, useSelector} from "react-redux";
import {updateUserInfo, updateUserPassportImages} from "features/profile/model/ProfileAction.js";
import ProfileField from "../../../../features/profile/account/profileField/ui/index.jsx";
import DocumentTypeSelector from "../../../../features/profile/account/documentTypeSelector/ui/index.jsx";
import PassportPhotos from "../../../../features/profile/account/passportPhotos/ui/index.jsx";
import {useTranslation} from "react-i18next";

function ProfileView() {
    const user = useSelector(state => state.auth.user?.user);
    const dispatch = useDispatch()
    const {t} = useTranslation()
    const [isEditing, setIsEditing] = useState(false);
    const [editField, setEditField] = useState(null);
    const [showSaveButton, setShowSaveButton] = useState(false);


    const methods = useForm({
        defaultValues: {
            Nickname: user?.Profile?.Nickname || '',
            Email: user?.Email || '',
            PhoneNumber: user?.Profile.PhoneNumber || '',
            documentType: '',
            idPassportPhotoFront: user?.IDPassportPhotoFront || null,
            idPassportPhotoBack: user?.IDPassportPhotoBack || null,
            idPassportPhotoWithClient: user?.IDPassportPhotoWithClient || null,
            internationalPassportPhoto: user?.InternationalPassportPhoto || null,
            internationalPassportPhotoWithClient: user?.InternationalPassportPhotoWithClient || null,
        }
    });


    const { handleSubmit, watch, formState: { isDirty }, setValue, getValues } = methods;
    const documentType = watch('documentType');


    useEffect(() => {
        const formValues = getValues();
        setShowSaveButton(isDirty ||
            formValues.idPassportPhotoFront ||
            formValues.idPassportPhotoBack ||
            formValues.idPassportPhotoWithClient ||
            formValues.internationalPassportPhoto ||
            formValues.internationalPassportPhotoWithClient
        );
    }, [isDirty, watch]);

    const hasPassportPhotos = user?.Profile?.Passport?.IDPassportPhotoFront || user?.Profile?.Passport?.IDPassportPhotoBack || user?.Profile?.Passport?.IDPassportPhotoWithClient || user?.Profile?.Passport?.InternationalPassportPhoto || user?.Profile?.Passport?.InternationalPassportPhotoWithClient;

    const handleEditClick = (field) => {
        setEditField(field);
        setIsEditing(true);
    };

    const handleCancel = () => {
        setIsEditing(false);
        setEditField(null);
    };

    const handleSave = (data) => {
        dispatch(updateUserInfo(data));
        setIsEditing(false);
        setEditField(null);
    };

    const handleSaveImages = async () => {
        const formValues = getValues();
        const formData = new FormData();

        if (formValues.idPassportPhotoFront) formData.append('IDPassportPhotoFront', formValues.idPassportPhotoFront.file);
        if (formValues.idPassportPhotoBack) formData.append('IDPassportPhotoBack', formValues.idPassportPhotoBack.file);
        if (formValues.idPassportPhotoWithClient) formData.append('IDPassportPhotoWithClient', formValues.idPassportPhotoWithClient.file);
        if (formValues.internationalPassportPhoto) formData.append('InternationalPassportPhoto', formValues.internationalPassportPhoto.file);
        if (formValues.internationalPassportPhotoWithClient) formData.append('InternationalPassportPhotoWithClient', formValues.internationalPassportPhotoWithClient.file);

        try {
            await dispatch(updateUserPassportImages(formData)).unwrap();

            setShowSaveButton(false);
            console.log("Изображения успешно загружены!");
        } catch (error) {
            console.error("Ошибка при загрузке изображений:", error);
        }
    };

    const handleClearImage = (imageType) => {
        setValue(imageType, null);
    };

    console.log(showSaveButton, 'showSaveButton')

    return (
            <div className="sm:ml-5 lg:ml-10 w-full h-100">
                <h2 className="text-xl md:text-2xl font-bold mb-4">{t('account.personalInformation')}</h2>
                <FormProvider {...methods}>
                    <div className="flex flex-col gap-5">
                        {['Nickname', 'Email', 'PhoneNumber'].map((field, index) => (
                            <ProfileField
                                key={index}
                                field={field}
                                isEditing={isEditing}
                                editField={editField}
                                handleEditClick={handleEditClick}
                                handleCancel={handleCancel}
                                handleSubmit={handleSubmit}
                                handleSave={handleSave}
                            />
                        ))}
                        {!hasPassportPhotos ? (
                            <DocumentTypeSelector />
                        ) : (
                            <div className="flex flex-col mb-2 w-max">
                                <span className='font-bold text-base'>{t('account.accountVerified')}</span>
                            </div>
                        )}
                        {documentType && (
                            <PassportPhotos
                                documentType={documentType}
                                handleClearImage={handleClearImage}
                                setValue={setValue}
                            />
                        )}
                    </div>
                    {showSaveButton && (
                        <div className='flex justify-center mt-3 md:mt-4'>
                            <Button name={t('button.save')}
                                    type="button"
                                    style='bg-blue-500 text-white sm:hover:bg-blue-400'
                                    onClick={handleSaveImages}/>
                        </div>
                    )}
                </FormProvider>
            </div>
    );
}

export default ProfileView;
