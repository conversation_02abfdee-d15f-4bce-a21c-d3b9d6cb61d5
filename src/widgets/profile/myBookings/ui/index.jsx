import {useDispatch, useSelector} from "react-redux";
import {useEffect, useState} from "react";
import {getMyBookings} from "../../../../store/actions/booking/Booking.js";
import MyBookingCard from "../../../../entities/myBookingCard/MyBookingCard.jsx";
import {getUserInfo} from "../../../../store/actions/auth/Auth.js";
import {getAccommodationByID} from "../../../../store/actions/accommodations/Accommodation";
import Button from "../../../../shared/ui/button/Button.jsx";
import {useLocation} from "react-router-dom";

function MyBookings() {
    const dispatch = useDispatch()
    const location = useLocation();

    const myBookings = useSelector(state => state.booking?.bookings)
    const ownerInfo = useSelector(state => state.auth?.userInfo);
    const accommodations = useSelector(state => state.accommodations?.accommodationByID);
    const [activeTab, setActiveTab] = useState(location.state?.status || 'pending');


    useEffect(() => {
        dispatch(getMyBookings({status: activeTab, limit: '', page: ''}))
    },[dispatch, activeTab])

    useEffect(() => {
        myBookings.forEach(booking => {
            dispatch(getUserInfo(booking.UserID));
            dispatch(getAccommodationByID(booking.AccommodationID));
        });
    }, [myBookings, dispatch]);



    const clickStatus = (status) => {
        setActiveTab(status);
    };

    const tabs = [
        { name: 'В ожидании', status: 'pending' },
        { name: 'В процессе', status: 'active' },
        { name: 'Отклоненные', status: 'rejected' }
    ];

    return (
        <div className='sm:ml-5 lg:ml-10 w-full'>
            <h2 className="text-2xl font-bold mb-4">Мои бронирования</h2>
            <div className='flex space-x-4 mb-4'>
                {tabs.map(tab => (
                    <Button
                        key={tab.status}
                        name={tab.name}
                        onClick={() => clickStatus(tab.status)}
                        style={`${activeTab === tab.status ? ' border-blue-600' : ''} text-sm md:text-base rounded-none `}
                    />
                ))}
            </div>
            <div className='pr-5 lg:pr-0 grid grid-cols-1 md:grid-cols-1 xl:grid-cols-2 gap-5'>
                {myBookings.length > 0 ? (
                    myBookings.map(booking => {
                        return(
                            <MyBookingCard
                                key={booking.ID}
                                booking={booking}
                                OwnerID={booking.Accommodation.OwnerID}
                            />
                        )
                    })
                ) : (
                    <p className="text-gray-500">Нет активных бронирований.</p>
                )}
            </div>
        </div>
    );
}

export default MyBookings;