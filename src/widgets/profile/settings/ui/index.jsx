import { Link, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { deleteMyAccount } from "store/actions/auth/Auth";
import { toast } from "react-toastify";
import Button from "../../../../shared/ui/button/Button.jsx";
import {useState} from "react";
import Modal from "../../../../shared/ui/modal/Modal.jsx";

function ProfileSettings() {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const [isModalOpen, setIsModalOpen] = useState(false);

    const user = useSelector((state) => state.auth.user);
    const role = user?.user?.Role;
    const token = user?.token;
    const isDeleteRequested = user?.user?.delete_request === true;

    console.log(user, 'user')



    const settingsLink = [
        // { name: 'Личная информация', description: 'Измените личные данные', path: '/profile/personal-info' },
        { name: 'Cменить пароль', description: 'Измените пароль', path: '/auth/change-password' },
        { name: 'Уведомления', description: 'Настройки уведомлений', path: '/profile/notifications' },
        { name: 'Платежи и выплаты', description: 'Просмотр и управление платежами', path: '/profile/payments' },
    ];

    const handleDelete = async () => {
        if (!token || !role) return;

        const result = await dispatch(deleteMyAccount({ token, role }));
        setIsModalOpen(false);

        if (deleteMyAccount.fulfilled.match(result)) {
            if (role === 'client') {
                toast.success("Аккаунт успешно удалён.");
                navigate('/');
            } else if (role === 'owner') {
                toast.info("Запрос на удаление отправлен. Ожидайте подтверждения модератора.");
            }
        }
    };




    return (
        <div className='sm:ml-5 lg:ml-10'>
            <h2 className='text-2xl font-bold mb-4'>Настройки профиля</h2>

            <div className='grid lg:grid-cols-2 gap-4 md:w-max font-semibold w-full mx-auto'>
                {
                    settingsLink.map((item) => (
                        <Link key={item.name} to={item.path}
                              className='flex flex-col md:justify-center bg-white border rounded-lg p-4 cursor-pointer'>
                            <span>{item.name}</span>
                            <span className='text-sm text-gray-500'>{item.description}</span>
                        </Link>
                    ))
                }
            </div>
            {isDeleteRequested ? (
                <p className='mt-10 font-semibold'>Удаление аккаунта на рассмотрении</p>
            ):(
                <div className="mt-10 w-max">
                    <Button
                        name='Удалить аккаунт'
                        onClick={() => setIsModalOpen(true)}
                        style="w-full bg-red-600 text-white hover:bg-red-700"
                    />
                </div>
            )}

            <Modal
                isOpen={isModalOpen}
                onClose={() => setIsModalOpen(false)}
                onConfirm={handleDelete}
                title="Удаление аккаунта"
                confirmText={role === 'owner' ? 'Отправить запрос' : 'Удалить'}
                cancelText="Отмена"
                crossClose={true}
                confirmButtonProps={{
                    className: "w-max border px-5 py-2 bg-red-600 text-white font-semibold rounded-md hover:bg-red-700"
                }}
            >
                <div className="text-center space-y-1 mb-5">
                    {role === 'owner' ? (
                        <>
                            <p>Вы действительно хотите отправить запрос на удаление аккаунта?</p>
                            <p className="text-gray-600 ">Модератор рассмотрит его вручную.</p>
                        </>
                    ) : (
                        <>
                            <p>Вы уверены, что хотите безвозвратно удалить аккаунт?</p>
                            <p className="text-gray-600 ">Это действие необратимо.</p>
                        </>
                    )}
                </div>
            </Modal>

        </div>
    );
}

export default ProfileSettings;
