import { useRef, useState } from "react";
import { useDispatch } from "react-redux";
import AvatarSection from "features/profile/sidebar/avatarSection/ui/index.jsx";
import PhotoView from "shared/ui/modal/PhotoView/index.jsx";
import {updateUserAvatar} from "features/profile/model/ProfileAction.js";
import ProfileInfo from "features/profile/sidebar/profileInfo/ui/index.jsx";
import PageLinks from "features/profile/sidebar/profileLinks/ui/index.jsx";

const ProfileSidebar = () => {
    const [avatarPreview, setAvatarPreview] = useState(null);
    const avatarInputRef = useRef(null);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const dispatch = useDispatch()

    const handleUpload = () => {
        const file = avatarInputRef.current.files[0];
        if (file) {
            dispatch(updateUserAvatar({ file }));
            setIsModalOpen(false);
        }
    };

    const handleCancel = () => {
        setAvatarPreview(null);
        setIsModalOpen(false);
    };

    return (
        <div className='flex-col md:border-r-2 sm:pr-3'>
            <div className='flex flex-col md:w-[250px] items-center gap-2 md:gap-5 bg-gray-200 py-4 rounded-lg'>
                <AvatarSection
                    setAvatarPreview={setAvatarPreview}
                    setIsModalOpen={setIsModalOpen}
                    avatarInputRef={avatarInputRef}
                />
                <ProfileInfo/>
            </div>
            <PageLinks />
            {isModalOpen && (
                <PhotoView
                    preview={avatarPreview}
                    handleCancel={handleCancel}
                    handleUpload={handleUpload}
                />
            )}
        </div>
    );
};

export default ProfileSidebar;
