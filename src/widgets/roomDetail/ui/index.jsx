import React, { useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import notFound from '/public/noImg.jpg'
import {getAccommodationByID} from "../../../store/actions/accommodations/Accommodation";
import {getUserInfo} from "../../../store/actions/auth/Auth.js";
import PhotoGallery from "../../../features/accommodationDetail/photoGallery/ui/index.jsx";
import AccommodationDetailInfo from "../../../features/accommodationDetail/accommodationDetailInfo/ui/index.jsx";
import BookingBar from "../../../features/accommodationDetail/bookingBar/ui/index.jsx";
import DetailPageSkeleton from "../../../shared/ui/skeleton/detailPageSkeleton/DetailPageSkeleton.jsx";
import DetailMap from "../../../features/accommodationDetail/detailMap/index.jsx";

const  AccommodationDetail = () => {
    const { id } = useParams();
    const dispatch = useDispatch();

    const numericId = parseInt(id ?? '', 10);

    const accommodationsId = useSelector(state => state.accommodations?.accommodationByID[numericId]?.Accommodation);
    const loading = useSelector(state => state.accommodations?.status);
    const calendar = useSelector(state => state.accommodations?.accommodationByID[numericId]?.Calendar);
    const ownerID = accommodationsId?.OwnerID;
    const user = useSelector(state => state.auth.userInfo[ownerID]);


    useEffect(() => {
        if (numericId && !accommodationsId) {
            dispatch(getAccommodationByID(numericId));
        }
    }, [dispatch, numericId, accommodationsId]);

    useEffect(() => {
        if (ownerID && !user) {
            dispatch(getUserInfo(ownerID));
        }
    }, [dispatch, ownerID, user]);

    return (
        <div className='w-full'>
            {ownerID && user && accommodationsId ? (
                <div className='flex flex-col lg:flex-row gap-10'>
                    <div>
                        <PhotoGallery
                            photos={accommodationsId.Images}
                            title={accommodationsId.Title}
                        />
                        <AccommodationDetailInfo
                            room={accommodationsId}
                            owner={user}
                            ownerID={ownerID}
                        />
                        <DetailMap location={accommodationsId}/>
                    </div>
                    <div className='relative sm:w-1/2 '>
                        <BookingBar
                            room={accommodationsId}
                            calendar={calendar}
                        />
                    </div>
                </div>
            ) : (
                <DetailPageSkeleton/>
            )}
        </div>
    );
}

export default AccommodationDetail;
