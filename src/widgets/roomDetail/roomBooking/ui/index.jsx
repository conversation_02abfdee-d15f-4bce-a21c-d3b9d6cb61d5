import Button from "../../../../shared/ui/button/Button.jsx";
import { useState, useMemo } from "react";
import { useDispatch, useSelector } from 'react-redux';
import {createNewBooking} from "../../../../store/actions/booking/Booking.js";
import {useNavigate} from "react-router-dom";
import { fetchProfile } from "store/actions/auth/Auth.js";
import { useEffect } from "react";

const RoomBooking = ({ room }) => {
      
    const [checkInDate, setCheckInDate] = useState("");
    const [checkOutDate, setCheckOutDate] = useState("");
    const [guestCount, setGuestCount] = useState(1);
    const [childrenCount, setChildrenCount] = useState(0);

    const dispatch = useDispatch();
    const navigate = useNavigate();
    const { status, error, loading } = useSelector((state) => state.booking);
    const profile = useSelector((state) => state.auth.user?.profile);

    const handleCheckInDateChange = (e) => {
        setCheckInDate(e.target.value);
    };

    const handleCheckOutDateChange = (e) => {
        setCheckOutDate(e.target.value);
    };

    const handleGuestCountChange = (e) => {
        setGuestCount(e.target.value);
    };

    const handleChildrenCountChange = (e) => {
        setChildrenCount(e.target.value);
    };

    const calculateTotalNights = () => {
        if (checkInDate && checkOutDate) {
            const checkIn = new Date(checkInDate);
            const checkOut = new Date(checkOutDate);
            const diffTime = Math.abs(checkOut - checkIn);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            return diffDays;
        }
        return 0;
    };


    const totalNights = useMemo(() => calculateTotalNights(), [checkInDate, checkOutDate]);

    const calculateTotalCost = () => {
        return totalNights * room.DiscountPrice;
    };

    const totalCost = useMemo(() => calculateTotalCost(), [totalNights, room.DiscountPrice]);
  useEffect(() => {
        if (!profile) {
            dispatch(fetchProfile());
        }
    }, [dispatch, profile]);
    const handleBooking = () => {
        if (profile && profile.Name && profile.Surname && profile.PhoneNumber) {
        //     const bookingData = {
        //         AccommodationID: room.ID,
        //         StartDate: checkInDate,
        //         EndDate: checkOutDate,
        //         PeopleQuantity: parseInt(guestCount, 10),
        //         Days: totalNights,
        //         TotalSum: totalCost
        //     };
    
        //     dispatch(createNewBooking(bookingData))
        //         .then(() => {
        //             navigate('/profile/my-bookings');
        //         })
        //         .catch((error) => {
        //             console.error("Ошибка при создании бронирования:", error);
        //         });
        } else {
            const bookingData = {
                checkInDate,
                checkOutDate,
                guestCount,
                childrenCount,
                room, 
            };
        
            navigate(`/reservation/${room.ID}`, { state: bookingData });
        }
    };
    return (
        <div className=' mt-4 border p-5 shadow-xl rounded-md'>
            <h2 className="text-xl font-semibold mb-4">{room.DiscountPrice} сом за сутки</h2>
            <div className='flex gap-4'>
                <div className="mb-4">
                    <label className="block text-gray-700 font-semibold">Прибытие</label>
                    <input
                        type="date"
                        value={checkInDate}
                        onChange={handleCheckInDateChange}
                        className="w-full border rounded px-2 py-1"
                    />
                </div>
                <div className="mb-4">
                    <label className="block text-gray-700 font-semibold">Выезд</label>
                    <input
                        type="date"
                        value={checkOutDate}
                        onChange={handleCheckOutDateChange}
                        className="w-full border rounded px-2 py-1"
                    />
                </div>
            </div>
            <div className="mb-4">
                <label className="block text-gray-700 font-semibold">Количество взрослых гостей</label>
                <select
                    className="w-full border rounded px-2 py-1"
                    value={guestCount}
                    onChange={handleGuestCountChange}
                >
                    {Array.from({length: room.PeopleQuantity}, (_, i) => (
                        <option key={i + 1} value={i + 1}>
                            {i + 1}
                        </option>
                    ))}
                </select>
            </div>
            <div className="mb-4">
                <label className="block text-gray-700 font-semibold">Количество детей</label>
                <input
                    type="number"
                    min="0"
                    value={childrenCount}
                    onChange={handleChildrenCountChange}
                    className="w-full border rounded px-2 py-1"
                />
            </div>
            <div className="mb-4">
                <p className="text-red-600 font-semibold">С животными нельзя</p>
            </div>

            <Button
                style='bg-blue-500 text-white w-full'
                name="Забронировать"
                onClick={handleBooking}
            />
            {checkInDate && checkOutDate && (
                <div className="flex flex-col mt-4 gap-3 border-t">
                    <div className='flex justify-between pt-4'>
                        <p className='font-semibold'>
                            {room.DiscountPrice} сом
                            х {totalNights} {totalNights > 1 ? 'суток' : 'сутки'}
                        </p>
                        <p className='font-semibold'>
                            {totalCost} сом
                        </p>
                    </div>
                    <div className='flex justify-between'>
                        <p className='font-semibold'>
                            Итого:
                        </p>
                        <p className='font-semibold'>
                            {totalCost} сом
                        </p>
                    </div>
                </div>
            )}

            {loading && <p>Загрузка...</p>}
            {error && <p className="text-red-600">{error}</p>}
            {status === 'succeeded' && <p className="text-green-600">Бронирование успешно!</p>}
        </div>
    );
};

export default RoomBooking;

