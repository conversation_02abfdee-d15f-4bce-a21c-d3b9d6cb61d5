// hooks/useWebSocket.js
import { useEffect } from 'react';
import { useSelector} from 'react-redux';
import {WS} from '../config/api.js'

const useWebSocket = (notificationCallback) => {
    const token = useSelector((state) => state.auth.user.token);
    useEffect(() => {
        if (!token) return;
        const ws = new WebSocket(`${WS}?token=${token}`);
        ws.onmessage = (event) => {
            const notification = JSON.parse(event.data);
            if (notificationCallback) {
                notificationCallback(notification);
            }
        };
        return () => {
            ws.close();
        };
    }, [token]);

};

export default useWebSocket;
