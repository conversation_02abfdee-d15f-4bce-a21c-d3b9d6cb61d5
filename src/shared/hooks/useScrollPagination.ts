import { useEffect } from 'react';

interface UseScrollHandlerOptions {
    isLoading: boolean;
    hasMore: boolean;
    onLoadMore: () => void;
}

export const useScrollPagination = ({ isLoading, hasMore, onLoadMore }: UseScrollHandlerOptions) => {
    useEffect(() => {
        const scrollHandler = () => {
            const scrollTop = document.documentElement.scrollTop;
            const scrollHeight = document.documentElement.scrollHeight;
            const clientHeight = window.innerHeight;

            // console.log(scrollTop + clientHeight, 'FULL')
            // console.log(scrollHeight, 'scrollHeight')

            // Проверяем, достиг ли пользователь конца страницы
            if (scrollTop + clientHeight >= scrollHeight - 90 && !isLoading && hasMore) {
                onLoadMore(); // Вызываем функцию для загрузки следующей страницы
            }
        };

        // Добавляем обработчик скролла
        document.addEventListener('scroll', scrollHandler);

        return () => {
            // Удаляем обработчик скролла при размонтировании
            document.removeEventListener('scroll', scrollHandler);
        };
    }, [isLoading, hasMore, onLoadMore]);
};
