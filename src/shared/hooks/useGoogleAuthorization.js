import { useGoogleLogin } from "@react-oauth/google";
import { useNavigate } from "react-router-dom";
import { googleLogin } from "store/actions/auth/Auth";
import {useDispatch} from "react-redux";

const useGoogleAuthorization = () => {
    const navigate = useNavigate();
    const dispatch = useDispatch()
    const sendGoogleLogin = useGoogleLogin({
        onSuccess: async (tokenResponse) => {
            const token = tokenResponse.access_token;
            dispatch(googleLogin({ token, navigate }))
        },
        onError: (error) => console.error('Login Failed:', error)
    });
    return sendGoogleLogin
}

export default useGoogleAuthorization;