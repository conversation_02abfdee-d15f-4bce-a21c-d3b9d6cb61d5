import React, { useState, useEffect, useRef } from 'react';

const useActiveOutsideClick = (initialValue) => {
    const [isActive, setIsActive] = useState(initialValue);
    const ref = useRef(null);

    const handleClick = (e) => {
        if (ref.current && !ref.current.contains(e.target)) {
            setIsActive(false);
        }
    };

    useEffect(() => {
        document.addEventListener("click", handleClick);
        return () => {
            document.removeEventListener("click", handleClick);
        };
    }, []);

    return { ref, isActive, setIsActive };
};

const useLatest = (value) => {
    const valueRef = React.useRef(value);

    React.useLayoutEffect(() => {
        valueRef.current = value;
    }, [value]);

    return valueRef;
}

const useOutsideClick = (elementRef, handler, attached = true) => {
    const latestHandler = useLatest(handler);

    React.useEffect(() => {
        if (!attached) return;

        const handleClick = (e) => {
            if (!elementRef.current) return;
            if (!elementRef.current.contains(e.target)) {
                latestHandler.current();
            }
        };

        document.addEventListener('click', handleClick);

        return () => {
            document.removeEventListener('click', handleClick);
        };
    }, [elementRef, latestHandler, attached]);
}

export { useOutsideClick };

export default useActiveOutsideClick;
