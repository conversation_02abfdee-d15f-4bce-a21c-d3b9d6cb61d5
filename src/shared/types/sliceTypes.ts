

export interface GetAccommodationsParams {
    page: number;
    limit: number;
    title?: string;
    category_id?: number | null; // Разрешаем null для необязательного параметра
    people_quantity?: number;
    rooms_quantity?: number;
    min_price?: number;
    max_price?: number;
    facilities?: number;
    min_rating?: number;
}



export type dataList = {
    id?: number;
    text: string;
    icon?: JSX.Element,
    menuTitle?: string
};

export type Image = {
    images: string[];
    style: React.CSSProperties;
    ImageByBool: boolean;
}
export interface SliderPropsWithData extends Image {
    items: Array<{ id: number; text: string; image: string }>;
    children?: React.ReactNode;

}

export type PopoverModule = {
    open: boolean,
    close: () => void,
    className?: string,
    style?: React.CSSProperties,
    children?: React.ReactNode;

}

export interface Images {
    ID: number;
    CreatedAt: string;
    UpdatedAt: string;
    DeletedAt: string | null;
    ImageURL: string;
}

export interface Category {
    ID: number;
    CreatedAt: string;
    UpdatedAt: string;
    DeletedAt: string | null;
    Slug: string;
    Name: string;
}


export interface FacilityCategory {
    ID: number;
    CreatedAt: string;
    UpdatedAt: string;
    DeletedAt: string | null;
    Value: string;
    Facilities: any;
}
export interface Accommodation {
    ID: number;
    CreatedAt: string;
    UpdatedAt: string;
    DeletedAt: string | null;
    Title: string;
    Description: string;
    Price: number;
    DiscountPrice: number;
    LocationLabel: string;
    Latitude: string;
    Longitude: string;
    Rating: string;
    ReviewsQuantity: number;
    RoomsQuantity: number;
    PeopleQuantity: number;
    IsAvailable: boolean;
    OwnerID: number;
    PriceDescription: string;
    CategoryID: number;
    Category: Category;
    Images: Images[];
}

export interface AccommodationState {
    accommodations: Accommodation[];
    category: Category[];
    facility: FacilityCategory[];
    status: 'idle' | 'loading' | 'succeeded' | 'failed';
    error: string | null;
    count:number
    hasMore: boolean;
    accommodationByID: { [key: number]: Accommodation };
    accommodationAllByID: number[];
    myAccommodations: Accommodation[];
    id: number|null;
}


export interface CreateAccomondationGalleryPayload {
    formData: FormData;
    id: string;
}







