// export const handleImageUpload = (e, setImage) => {
//     const file = e.target.files[0];
//     if (file) {
//         const reader = new FileReader();
//         reader.onloadend = () => {
//             setImage(reader.result);
//         };
//         reader.readAsDataURL(file);
//     }
// };


export const handleImageUpload = (e, setImage) => {
    const file = e.target.files[0];
    if (file) {
        const previewURL = URL.createObjectURL(file);
        setImage({ file, previewURL });
    }
};