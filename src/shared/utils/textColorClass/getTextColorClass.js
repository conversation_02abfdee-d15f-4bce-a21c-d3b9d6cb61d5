export const getTextColorClass = (text) => {
    const positiveKeywords = ['одобрено', 'успешно', 'подтверждено', 'заявка']; // ключевые слова для положительных сообщений
    const negativeKeywords = ['отклонено', 'ошибка', 'неуспешно']; // ключевые слова для отрицательных сообщений

    // Проверка на наличие ключевых слов в тексте (игнорируется регистр)
    const isPositive = positiveKeywords.some(keyword => text.toLowerCase().includes(keyword));
    const isNegative = negativeKeywords.some(keyword => text.toLowerCase().includes(keyword));

    if (isPositive) {
        return 'bg-green-500'; // Класс для положительных сообщений (зеленый текст)
    } else if (isNegative) {
        return 'bg-red-500'; // Класс для отрицательных сообщений (красный текст)
    } else {
        return 'bg-gray-700'; // Класс для нейтральных сообщений (серый текст)
    }
}