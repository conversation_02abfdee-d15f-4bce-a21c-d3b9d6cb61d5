import { useState, useEffect } from 'react';

const useResponsiveMonths = (defaultMonths = 2, smallScreenWidth = 600) => {
    const [monthsToShow, setMonthsToShow] = useState(defaultMonths);

    useEffect(() => {
        const handleResize = () => {
            if (window.innerWidth < smallScreenWidth) {
                setMonthsToShow(1); // Для маленьких экранов (меньше 600px) показываем 1 месяц
            } else {
                setMonthsToShow(defaultMonths); // Для больших экранов показываем по умолчанию 2 месяца
            }
        };

        // Вызов функции при первой загрузке компонента
        handleResize();

        // Добавляем обработчик изменения размера окна
        window.addEventListener('resize', handleResize);

        // Удаляем обработчик при размонтировании компонента
        return () => {
            window.removeEventListener('resize', handleResize);
        };
    }, [defaultMonths, smallScreenWidth]);

    return monthsToShow;
};

export default useResponsiveMonths;
