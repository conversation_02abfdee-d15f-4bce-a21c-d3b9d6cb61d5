import React, { useState } from 'react';
import { format, startOfMonth, endOfMonth, addMonths, subMonths, startOfWeek, endOfWeek, addDays, isBefore, isAfter, isEqual, parseISO } from 'date-fns';
import { ru } from 'date-fns/locale';

const daysOfWeek = ['Вс', 'Пн', 'Вт', 'Ср', 'Чт', 'Пт', 'Сб'];
const currentDate = new Date();

export function isSameDay(a, b) {
    return format(a, 'yyyy-MM-dd') === format(b, 'yyyy-MM-dd');
}

export function isSameMonth(a, b) {
    return format(a, 'MM-yyyy') === format(b, 'MM-yyyy');
}

export function isBooked(date, bookings) {
    for (const booking of bookings) {
        const startDate = parseISO(booking.StartDate);
        const endDate = parseISO(booking.EndDate);
        if (isEqual(date, startDate) || isEqual(date, endDate) || (isAfter(date, startDate) && isBefore(date, endDate))) {
            return booking;
        }
    }
    return null;
}

const Calendar = ({ bookings }) => {
    const [currentMonth, setCurrentMonth] = useState(new Date());
    const [selectedBooking, setSelectedBooking] = useState(null);

    const handlePrevMonth = () => {
        setCurrentMonth(subMonths(currentMonth, 1));
    };

    const handleNextMonth = () => {
        setCurrentMonth(addMonths(currentMonth, 1));
    };

    const renderHeader = () => {
        return (
            <div className="flex justify-center gap-5 items-center mb-4">
                <button onClick={handlePrevMonth} className="bg-blue-500 text-white px-2 py-1 md:px-4 md:py-2 rounded">
                    &lt;
                </button>
                <div>
                    <span className="text-lg md:text-xl font-semibold">
                        {format(currentMonth, 'MMMM yyyy', { locale: ru })}
                    </span>
                </div>
                <button onClick={handleNextMonth} className="bg-blue-500 text-white px-2 py-1 md:px-4 md:py-2 rounded">
                    &gt;
                </button>
            </div>
        );
    };

    const renderDaysOfWeek = () => {
        return (
            <div className="grid grid-cols-7">
                {daysOfWeek.map((day, index) => (
                    <div key={index} className="text-center font-bold border border-gray-300 h-10 flex items-center justify-center ">
                        {day}
                    </div>
                ))}
            </div>
        );
    };

    const renderCells = () => {
        const monthStart = startOfMonth(currentMonth);
        const monthEnd = endOfMonth(currentMonth);
        const startDate = startOfWeek(monthStart);
        const endDate = endOfWeek(monthEnd);

        const rows = [];
        let days = [];
        let day = startDate;
        let formattedDate = '';

        while (day <= endDate) {
            for (let i = 0; i < 7; i++) {
                formattedDate = format(day, 'd');
                const cloneDay = day;
                const isPast = isBefore(day, currentDate);
                const booking = isBooked(day, bookings);
                days.push(
                    <div
                        key={day}
                        className={`border border-gray-200 h-16 flex items-center justify-center hover:bg-gray-100 ${
                            !isSameMonth(day, monthStart) ? 'bg-gray-100' : isPast ? 'bg-gray-100' : booking ? 'bg-red-300 cursor-pointer hover:bg-red-300' : 'bg-white'
                        }`}
                        onClick={() => booking && setSelectedBooking(booking)}
                    >
                        <span
                            className={`${
                                isSameDay(day, new Date()) ? 'bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center' : ''
                            }`}
                        >
                            {formattedDate}
                        </span>
                    </div>
                );
                day = addDays(day, 1);
            }
            rows.push(
                <div className="grid grid-cols-7" key={day}>
                    {days}
                </div>
            );
            days = [];
        }
        return <div>{rows}</div>;
    };

    const renderModal = () => {
        if (!selectedBooking) return null;
        return (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                <div className="bg-white p-5 rounded-lg shadow-lg">

                    <div className='flex flex-col gap-2 lg:text-xl'>
                        <p>Дата начала: <strong>{selectedBooking.StartDate}</strong></p>
                        <p>Дата окончания: <strong>{selectedBooking.EndDate}</strong></p>
                    </div>
                    <button onClick={() => setSelectedBooking(null)}
                            className="mt-4 bg-blue-500 text-white px-4 py-2 rounded">
                        Закрыть
                    </button>
                </div>
            </div>
        );
    };

    return (
        <div className="w-full md:p-5">
            {renderHeader()}
            {renderDaysOfWeek()}
            {renderCells()}
            {renderModal()}
        </div>
    );
};

export default Calendar;
