import React from 'react';
import { useSelector } from 'react-redux';
import { Navigate, Outlet } from 'react-router-dom';
import {selectIsModerator} from "../../../store/selectors/authRole.js";

// ProtectedRoute для модераторов
const ProtectedRoute = () => {
    const isModerator = useSelector(selectIsModerator);

    if (!isModerator) {
        return <Navigate to="/" replace />;
    }
    return <Outlet />;
};

export default ProtectedRoute;
