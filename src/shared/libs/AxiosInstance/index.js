import axios from 'axios';
import { toast } from 'react-toastify';

const API = import.meta.env.VITE_REACT_API_URL

const axiosInstance = axios.create({
  baseURL: API
});
axiosInstance.interceptors.request.use(async (config) => {
  await (() => new Promise((r) => setTimeout(r, 100)))();
  const state = JSON.parse(localStorage.getItem('userDetails'));
  const token = state?.token;
  config.params = config.params || {};
  config.headers['Authorization'] = `Bearer ${token}`;
  // config.headers["Access-Control-Allow-Origin"] = "*";
  // config.headers['Accept-Language'] = 'en';
  return config;
});

axiosInstance.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response) {
      const status = error.response.status;
      if (status === 401) {
        localStorage.removeItem('userDetails');
        window?.location?.replace('/');
      }

      if (status === 403) {
        toast.error('Доступ запрещен');
        // window?.location?.replace('/');
      }
    }
    return Promise.reject(error);
  }
);
const getRequestConfig = (args) => {
  if (typeof args === 'string') {
    return { url: args };
  }
  return args;
};

export const axiosBaseQuery = ({ prepareHeaders, meta, transformResponse } = {}) => {


  return async (args, api, extraOptions) => {
    var _a, _b;
    try {
      const requestConfig = getRequestConfig(args);
      const result = await axiosInstance(
        Object.assign(
          Object.assign(Object.assign({}, requestConfig), {
            headers: prepareHeaders
              ? prepareHeaders(requestConfig.headers || {}, api)
              : requestConfig.headers,
            signal: api.signal
          }),
          extraOptions
        )
      );
      return {
        data: transformResponse ? transformResponse(result.data) : result.data
      };
    } catch (err) {
      if (!axios.isAxiosError(err)) {
        return {
          error: err,
          meta
        };
      }
      return {
        error: {
          status: (_a = err.response) === null || _a === void 0 ? void 0 : _a.status,
          data: ((_b = err.response) === null || _b === void 0 ? void 0 : _b.data) || err.message
        },
        meta
      };
    }
  };
};



export default axiosInstance;
