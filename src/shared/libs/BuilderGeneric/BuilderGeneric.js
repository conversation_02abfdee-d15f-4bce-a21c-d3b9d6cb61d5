import { handlePending, handleRejected, handleFulfilled } from "./GenericReducers";

const mergeFunctions = (...functions) => {
    return (a, b) => {
        for (const func of functions) {
            func(a, b);
        }

    }
}

class GenericBuilder {
    actions = {}
    addFunction(type, func) {
        if (!this.actions[type] && !Array.isArray(this.actions[type])) {
            this.actions[type] = [func]
        } else {
            this.actions[type].push(func)
        }
    }
    constructor(builder, ...types) {
        this.builder = builder
        for (const type of types) {
            this.addFunction(type.pending, handlePending)
            this.addFunction(type.rejected, handleRejected)
            this.addFunction(type.fulfilled, handleFulfilled)
        }
    }
    pending(type, func) {
        this.addFunction(type.pending, func);
        return this
    }
    rejected(type, func) {
        this.addFunction(type.rejected, func);
        return this
    }
    fulfilled(type, func) {
        this.addFunction(type.fulfilled, func);
        return this
    }
    includeCases() {
        for (const type in this.actions) {
            this.builder.addCase(type, mergeFunctions(...this.actions[type]))
        }
    }
}
const createGenericBuilder = (builder, ...args) => {
    const genericBuilder = new GenericBuilder(builder, ...args);
    return genericBuilder;
}

export default createGenericBuilder;
