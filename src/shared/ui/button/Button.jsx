function Button({
    name,
    onClick,
    style = '',
    type,
    value,
    disabled = false,
    iconComponent = null,
    iconStyle = '',
    loading= false,
    customStyle = {},

}) {
    return (
        <button
            className={`relative ${style} border px-2 py-1 rounded-md text-center flex items-center justify-center ${
                disabled || loading ? 'cursor-not-allowed' : 'cursor-pointer'
            }`}
            type={type}
            onClick={onClick}
            value={value}
            disabled={disabled || loading}
            style={customStyle}
        >
            {iconComponent && (
                <p className={`absolute ${iconStyle} flex items-center`}>
                    {iconComponent}
                </p>
            )}
            <p className={`${iconComponent ? 'pl-7' : ''}`}>
                {name}
            </p>
        </button>
    );
}

export default Button;
