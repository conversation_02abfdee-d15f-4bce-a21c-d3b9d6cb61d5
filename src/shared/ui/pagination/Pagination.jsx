import React, { useState, useEffect } from 'react';

const Pagination = ({ totalPages, currentPage, onPageChange }) => {
    const [visiblePages, setVisiblePages] = useState([]);

    const handlePageChange = (page) => {
        let npage = page;
        if (page == "<<") {
            npage = 1
        }
        else if (page == ">>") {
            npage = totalPages
        }
        onPageChange(npage);
        updateVisiblePages(npage, totalPages);
    };

    const updateVisiblePages = (currentPage, totalPages) => {
        const visibleRange = 1; // Number of pages to show on each side of the current page
        const startPage = Math.max(currentPage - visibleRange, 1);
        const endPage = Math.min(currentPage + visibleRange, totalPages);
        const visiblePages = [];

        for (let i = startPage; i <= endPage; i++) {
            visiblePages.push(i);
        }

        if (startPage > 1) {
            visiblePages.unshift('<<');
        }

        if (endPage < totalPages) {
            visiblePages.push('>>');
        }

        setVisiblePages(visiblePages);
    };

    useEffect(() => {
        updateVisiblePages(currentPage, totalPages);
    }, [currentPage, totalPages]);

    return (
        <div className="flex justify-center space-x-1 text-gray-700 select-none mt-10">
            <button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="text-sm rounded-md bg-black text-white px-4 py-2 transition duration-300 hover:bg-gray-400 disabled:opacity-50 disabled:cursor-not-allowed"
            >
                Предыдущий
            </button>
            {visiblePages.map((page, index) => (
                <button
                    key={`page-${page}`}
                    onClick={() => handlePageChange(page)}
                    disabled={page === currentPage}
                    className={`text-sm rounded-md px-4 py-2 transition duration-300 ${page === currentPage
                        ? 'bg-black text-white'
                        : 'bg-gray-200 text-black hover:bg-black hover:text-white'
                    }`}
                >
                    {page}
                </button>
            ))}
            <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="text-sm rounded-md bg-black text-white px-4 py-2 transition duration-300 hover:bg-gray-400 disabled:opacity-50 disabled:cursor-not-allowed"
            >
                Следующий
            </button>
        </div>
    );
};

export default Pagination;
