import { useEffect, useRef, useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, TileLayer, useMap } from "react-leaflet";
import "leaflet/dist/leaflet.css";
import 'leaflet-geosearch/dist/geosearch.css';
import { GeoSearchControl, OpenStreetMapProvider } from 'leaflet-geosearch';
import { useFormContext } from "react-hook-form";
import axios from "axios";
import { GEOAPIFY_API } from "shared/config/api";
import {useTranslation} from "react-i18next";

const SearchField = ({ name }) => {
    const methods = useFormContext();
    const { getValues, setValue, watch } = methods;
    const markerRef = useRef(null);
    const [typingTimeout, setTypingTimeout] = useState(null);
    const [initialized, setInitialized] = useState(false); // Добавлен флаг инициализации

    const provider = new OpenStreetMapProvider();

    const searchControl = new GeoSearchControl({
        provider: provider,
        autoComplete: true,
        showMarker: false,
        style: 'hidden',
        autoCompleteDelay: 250,
    });

    const map = useMap();

    const country = watch("country");
    const city = watch("city");
    const address = watch("address");

    useEffect(() => {
        const loc = getValues(name);

        if (loc && loc.lat && loc.lng && !initialized) { // Проверка флага инициализации
            const lat = parseFloat(loc?.lat);
            const lng = parseFloat(loc?.lng);
            setValue("country", loc?.country);
            setValue("city", loc?.city);
            setValue("address", loc?.address);


            if (markerRef.current) {
                markerRef.current.setLatLng([lat, lng]);
            } else {
                const initialMarker = L.marker([lat, lng], { draggable: false, autoPan: true }).addTo(map);
                markerRef.current = initialMarker;
            }

            markerRef.current.bindPopup(`${loc?.address}, ${loc?.city}, ${loc?.country}`).openPopup();
            setInitialized(true); // Устанавливаем флаг, что данные были инициализированы
        }
    }, [map, getValues, name, country, city, address, initialized]);

    useEffect(() => {
        const updateMarkerFromInput = async () => {
            if (country && city && address) { // Проверка флага инициализации
                const query = ` ${country}, ${city}, ${address}`;
                try {
                    const response = await axios.get(`https://api.geoapify.com/v1/geocode/search?text=${encodeURIComponent(query)}&apiKey=${GEOAPIFY_API}`);

                    if (response.data.features.length > 0) {
                        const location = response.data.features[0].geometry.coordinates;
                        const lat = location[1];
                        const lng = location[0];

                        if (markerRef.current) {
                            markerRef.current.setLatLng([lat, lng]);
                        } else {
                            const newMarker = L.marker([lat, lng], { draggable: false, autoPan: true }).addTo(map);
                            markerRef.current = newMarker;
                        }

                        markerRef.current.bindPopup(`${address}, ${city}, ${country}`).openPopup();
                        setValue(name, { lat, lng, address, city, country });
                    }
                } catch (error) {
                    console.error("Error fetching location:", error);
                }
            }
        };

        if (typingTimeout) {
            clearTimeout(typingTimeout);
        }
        setTypingTimeout(setTimeout(updateMarkerFromInput, 500));
    }, [country, city, address, map, name, setValue, initialized]);

    useEffect(() => {
        map.addControl(searchControl);

        map.on('geosearch/showlocation', (e) => {
            if (initialized) { // Проверка флага инициализации
                if (markerRef.current) {
                    markerRef.current.setLatLng([e.location.raw.lat, e.location.raw.lng]);
                } else {
                    const newMarker = L.marker([e.location.raw.lat, e.location.raw.lng], { draggable: false, autoPan: true }).addTo(map);
                    markerRef.current = newMarker;
                }

                axios.get(`https://api.geoapify.com/v1/geocode/reverse?lat=${e.location.raw.lat}&lon=${e.location.raw.lng}&apiKey=${GEOAPIFY_API}`)
                    .then(response => {
                        const { country, city, street } = response?.data?.features?.[0].properties;
                        setValue("country", country);
                        setValue("city", city);
                        setValue("address", street);

                        markerRef.current.bindPopup(`${street}, ${city}, ${country}`).openPopup();
                    })
                    .catch(error => {
                        console.error("Error during reverse geocoding:", error);
                    });
            }
        });

        map.on('click', async function (e) {
                if (markerRef.current) {
                    markerRef.current.setLatLng([e.latlng.lat, e.latlng.lng]);
                } else {
                    const newMarker = L.marker([e.latlng.lat, e.latlng.lng], { draggable: false, autoPan: true }).addTo(map);
                    markerRef.current = newMarker;
                }

                try {
                    const response = await axios.get(`https://api.geoapify.com/v1/geocode/reverse?lat=${e.latlng.lat}&lon=${e.latlng.lng}&apiKey=${GEOAPIFY_API}`);
                    const { country, city, address_line2 } = response?.data?.features?.[0].properties;

                    setValue("country", country);
                    setValue("city", city);
                    setValue("address", address_line2);

                    markerRef.current.bindPopup(`${address_line2}, ${city}, ${country}`).openPopup();
                } catch (error) {
                    console.error("Error during reverse geocoding:", error);
                }
        });

        return () => {
            map.removeControl(searchControl);
            map.off("click");
        };
    }, [map, setValue, name, initialized]);

    return null;
};

const GeolocationInput = ({ name }) => {
    const {t} = useTranslation()
    const position = [42.8746, 74.6122];
    const methods = useFormContext();
    return (
        <>
            <div className='flex flex-col gap-8'>
                <div className=''>
                    <label className='font-bold block mb-2 text-base md:text-lg'>{t('createAccommodation.country')}:</label>
                    <input type="text" {...methods.register("country")} className="border p-2 w-full" />
                </div>
                <div className=''>
                    <label className='font-bold block mb-2 text-base md:text-lg'>{t('createAccommodation.city')}:</label>
                    <input type="text" {...methods.register("city")} className="border p-2 w-full" />
                </div>
                <div className=''>
                    <label className='font-bold block mb-2 text-base md:text-lg'>{t('createAccommodation.streetHouse')}:</label>
                    <input type="text" {...methods.register("address")} className="border p-2 w-full" />
                </div>
            </div>
            <MapContainer className="h-96 w-full my-2 z-0" center={position} zoom={13} scrollWheelZoom={true}>
                <TileLayer
                    attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                    url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                />
                <SearchField name={name} />
            </MapContainer>
        </>
    );
};

export default GeolocationInput;
