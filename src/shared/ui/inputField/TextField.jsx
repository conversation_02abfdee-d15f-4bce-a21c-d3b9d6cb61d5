import { Controller, useFormContext } from "react-hook-form";

const requiredMessage = 'Это обязательное поле!';

const TextField = ({
                       label,
                       name,
                       placeholder,
                       required,
                       validate,
                       min,
                       style,
                       rows = 3,
                       ...rest
                   }) => {
    const { control } = useFormContext();

    const rules = {
        required: required && requiredMessage,
        validate
    };

    return (
        <Controller
            control={control}
            name={name}
            rules={rules}
            defaultValue=''
            render={({ field, fieldState: { error } }) => (
                <div className='flex flex-col'>
                    <label className='font-bold mb-2 text-base md:text-lg'>{label}</label>
                    <textarea
                        {...field}
                        placeholder={placeholder}
                        rows={rows}
                        className={`${style} p-2 border rounded focus:outline-none focus:bg-white  ${error ? 'border-red-500' : 'border-gray-300'} ${rest.className}`}
                    />
                    {error ? <p className="text-red-500 mt-1">{error.message}</p> : <div className="mt-1">&nbsp;</div>}
                </div>
            )}
        />
    );
};

export default TextField;
