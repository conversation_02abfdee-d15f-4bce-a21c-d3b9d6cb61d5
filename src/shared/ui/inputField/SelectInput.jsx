    import React, { useEffect, useState } from "react";
    import { useF<PERSON><PERSON>ontex<PERSON>, Controller } from "react-hook-form";
    import { TiDelete } from "react-icons/ti";
    import { PAGE_SIZE } from "shared/config/api";
    import { useOutsideClick } from "shared/hooks/useOutsideClick";
    import axiosInstance from "shared/libs/AxiosInstance";
    import { useGetHandbookQuery } from "store/slices/handbook/handbookSlice";

    function useUrlSelectWithHandbook({ setOptions, setLoading, url, name }) {
        let validatedUrl = ""
        let validatedUrl2 = ""
        if (typeof url === "object" && url?.second) {
            validatedUrl = url.first?.includes('?')
                ? url.first + '&page_size=' + PAGE_SIZE
                : url.first + '?page_size=' + PAGE_SIZE;
            validatedUrl2 = url.second?.includes('?')
                ? url.second + '&page_size=' + PAGE_SIZE
                : url.second + '?page_size=' + PAGE_SIZE;
        } else {
            validatedUrl = url?.includes('?')
                ? url + '&page_size=' + PAGE_SIZE
                : url + '?page_size=' + PAGE_SIZE;
        }

        if (url){
            const { data, isLoading = false } = useGetHandbookQuery(url);
            const { data: data2, isLoading: isLoading2 = false } = useGetHandbookQuery(url);


            useEffect(() => {
                data && url && setOptions(data);
                setLoading(isLoading);
            }, [isLoading, url, data, isLoading2, data2]);
        }




        return null;
    }

    const SelectInput = ({
                             label,
                             name,
                             url,
                             list = [],
                             mapFunction = (i) => i,
                             required = false,
                             readonly = false,
                             disabled = false,
                             rules = {},
                             multi = false
                         }) => {
        const methods = useFormContext();
        const { clearErrors, setError } = methods;
        const selfRef = React.createRef();
        const [options, setOptions] = useState(list);
        const [loading, setLoading] = useState(false);
        const [selected, setSelected] = useState(multi ? [] : null);
        const [isVisible, setVisible] = useState(false);


        let requiredValue = false;

        if (required) {
            requiredValue =
                typeof required === 'string' || required instanceof String
                    ? required
                    : 'Это обязательное поле';
        }


        useOutsideClick(selfRef, () => setVisible(!isVisible)
            , isVisible);

        const buttonClickHandler = (e) => {
            e.preventDefault();
            e.stopPropagation();
            setVisible((p) => !p);
        };


        function selectHandler({ currentTarget }) {
            const id = currentTarget.getAttribute('data-value');
            if (multi) {
                const updatedValues = +id ? [...methods.getValues(name), +id] : methods.getValues(name);
                methods.setValue(name, updatedValues);
            } else {
                methods.setValue(name, +id || id);
                setVisible(false);
            }

            clearErrors(name);
        }

        function deleteSelection(id) {
            methods.setValue(name, [...methods.getValues(name).filter(idx => idx !== id)]);
        }

        useEffect(() => {
            if (multi) {
                const ids = methods.watch(name);
                const defaultSelections = options?.map(mapFunction)?.filter((item) => ids.includes(item?.id));
                if (defaultSelections.length) {
                    setSelected([...defaultSelections]);
                } else {
                    setSelected([]);
                }
            } else {
                const id = methods.watch(name);
                const defaultSelection = options?.map(mapFunction)?.find((item) => item?.id === id);
                if (defaultSelection?.title) {
                    setSelected(defaultSelection?.title);
                } else {
                    setSelected(null);
                }
            }
        }, [methods.watch(name), options]);

        useUrlSelectWithHandbook({ setOptions, name, url, setLoading });

        const hasError = !!methods.formState.errors[name];

        const isSelected = selected !== null && selected.length > 0;

        return <div className="w-full py-2 pb-6" >
            <Controller
                name={name}
                control={methods.control}
                defaultValue={multi ? [] : null}
                rules={{
                    required: requiredValue,
                    ...rules
                }}
                render={({ field }) => (
                    <div className="inline-block text-left dropdown w-full">
                        <div className="rounded-md shadow-sm w-full">
                            <label className="text-lg font-bold mb-2 block">
                                {label}
                            </label>
                            {multi ?
                                <div
                                    onClick={buttonClickHandler}
                                    disabled={readonly || disabled || !options?.length}
                                    className="w-full cursor-pointer text-start px-2 py-3 text-base font-medium leading-5 text-gray-700 transition duration-150 ease-in-out bg-white border border-gray-300 rounded-md hover:text-gray-500 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:bg-gray-50 active:text-gray-800"
                                >
                                    {loading ? 'Загрузка...' : selected.length ? selected.map((item) =>
                                        <div
                                            key={item?.id}
                                            className="bg-slate-300 mx-2 my-1 px-2 py-1 inline-flex items-center rounded-full "
                                        >{item?.title}
                                            <button className="inline-flex items-center ml-1" onClick={() => deleteSelection(item?.id)} >
                                                <TiDelete className="w-4 h-4" />
                                            </button>
                                        </div>) : (options.length ? 'Не выбрано' : "Нет данных")}
                                </div>
                                :
                                <button
                                    onClick={buttonClickHandler}
                                    disabled={readonly || disabled || !options?.length}
                                    className={`w-full text-start ${selected ? 'bg-white text-gray-500' : 'bg-white'} px-2 py-3 text-base font-medium leading-5 text-gray-400 transition duration-150 ease-in-out  border border-gray-300 rounded-md hover:text-gray-500 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:bg-gray-50 active:text-gray-800`}
                                    type="button" aria-haspopup="true" aria-expanded="true" aria-controls="headlessui-menu-items-117">

                                    {loading ? 'Загрузка...' : selected || (options.length ? 'Не выбрано' : "Нет данных")}
                                </button>
                            }

                        </div>
                <div ref={selfRef} name={name} className={`${!isVisible && "hidden opacity-0 invisible"} dropdown-menu transition-all duration-300 transform origin-top-right -translate-y-2 relative z-10`}>
                    <div className=" absolute right-0 w-full mt-2 origin-top-right bg-white border border-gray-200 divide-y divide-gray-100 rounded-md shadow-lg outline-none max-h-60 overflow-y-auto" aria-labelledby="headlessui-menu-button-1" id="headlessui-menu-items-117" role="menu">
                        {options
                            ?.map(mapFunction)
                            .map(({ id, title, lastTitle, middleText, isDefault }, _) => {
                                return (
                                    <li
                                        key={id}
                                        onClick={selectHandler}
                                        className={
                                            'border-y-2 p-1' + ` ${isDefault && 'bg-slate-200'} cursor-pointer  hover:bg-blue-400 hover:text-white`
                                        }
                                        data-title={title}
                                        data-value={id}
                                    >
                                        <div className={'flex justify-center align-items-center'} style={{ padding: '8px 12px' }}>
                                            <span className={`${middleText && lastTitle ? 'w-50' : 'w-100'} text-truncate`} style={{ fontWeight: 'bold' }}>{title}</span>
                                            {middleText && <span className="mx-2 text-muted">{middleText}</span>}
                                            {lastTitle && <span className="ml-auto text-right text-secondary">{lastTitle}</span>}
                                        </div>
                                    </li>
                                );
                            })}
                    </div>
                </div>
            </div>
                )}
            />
            {hasError && (
                <p className="text-red-500 mt-1">{methods.formState.errors[name]?.message}</p>
            )}
        </div>
    }

    export default SelectInput;
