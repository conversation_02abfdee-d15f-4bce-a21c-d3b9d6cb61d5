import { Controller, useFormContext } from "react-hook-form";
import PhoneInput from 'react-phone-number-input';
import 'react-phone-number-input/style.css';

const requiredMessage = 'Это обязательное поле!';
const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

const InputField = ({
                        label,
                        name,
                        type,
                        placeholder,
                        required,
                        validate,
                        min,
                        max,
                        validationRules = {},
                        style,
                        ...rest
                    }) => {
    const { control } = useFormContext();


    const rules = {
        required: required && requiredMessage,
        ...(type === 'email' && { pattern: { value: emailPattern, message: 'Неверный формат email' } }),
        ...validationRules,
    };


    return (
        <Controller
            control={control}
            name={name}
            rules={rules}
            defaultValue=''
            render={({ field, fieldState: { error } }) => (
                <div className='flex flex-col w-full'>
                    <label className='font-bold mb-2 text-base md:text-lg'>{label}</label>
                    {type === 'phone' ? (
                        <PhoneInput
                            placeholder={placeholder}
                            value={field.value}
                            onChange={field.onChange}
                            inputClass="border:none focus:outline-none focus:bg-white"
                            className={`${style} md:text-lg p-2 border rounded  focus:bg-white ${error ? 'border-red-500' : 'border-gray-300'}`}
                            {...rest}
                        />
                    ) : (
                        <input
                            {...field}
                            type={type}
                            value={field.value || ''}
                            onChange={(event) => {
                                const value = type == "number" ? +event?.target?.value : event?.target?.value;
                                field.onChange({ ...event, target: { ...event.target, value } });
                            }}

                            min={min}
                            max={max}
                            placeholder={placeholder}
                            className={`${style} p-2 border rounded focus:outline-none focus:bg-white  ${error ? 'border-red-500' : 'border-gray-300'} ${rest.className}`}
                        />
                    )}
                    {error ? <p className="text-red-500 mt-1">{error.message}</p> : <div className="mt-1">&nbsp;</div>}
                </div>
            )}
        />
    );
};

export default InputField;
