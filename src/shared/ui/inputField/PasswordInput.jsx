import { useState } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import { FaRegEyeSlash } from 'react-icons/fa';
import { MdOutlineRemoveRedEye } from 'react-icons/md';

const PasswordField = ({
    name,
    label,
    required,
    rules = {},
    placeholder
}) => {
    const { control, formState: { errors } } = useFormContext();
    const [isHidden, setIsHidden] = useState(true);
    const hide = () => {
        setIsHidden((p) => !p);
    };


    return (
        <Controller
            control={control}
            name={name}
            rules={{
                required: required ? 'Это поле обязательное' : false,
                ...rules
            }}
            defaultValue=""
            render={({ field }) => (
                <div className="flex flex-col relative w-full">
                    <label className="font-bold mb-2 md:text-lg">{label}</label>
                    <div className="relative w-full">
                        <input
                            type={isHidden ? 'password' : 'text'}
                            {...field}
                            className={`md:text-lg p-2 border rounded focus:outline-none focus:bg-white  ${errors[name] ? 'border-red-500' : 'border-gray-300'} w-full`}
                            placeholder={placeholder}
                        />
                        {
                            field.value &&
                            <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                                <button
                                    type="button"
                                    onClick={hide}
                                    className="absolute inset-y-0 right-0 flex items-center pr-3"
                                >
                                    {isHidden ? <MdOutlineRemoveRedEye /> : <FaRegEyeSlash />}
                                </button>
                            </div>
                        }
                    </div>
                    {errors[name] ? (
                        <p className="text-red-500 mt-1">{errors[name].message}</p>
                    ) : <p className="mt-1 text-white">1</p>}
                </div>
            )}
        />
    );
};

export default PasswordField;
