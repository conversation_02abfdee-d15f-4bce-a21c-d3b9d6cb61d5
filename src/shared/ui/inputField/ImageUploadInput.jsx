import { Button } from '@mui/material';
import { styled } from '@mui/system';
import { useState, useEffect } from "react";

const ImageUploadInput = ({ file, url, onRemove }) => {
    const [imageSrc, setImageSrc] = useState(null);

    useEffect(() => {
        let objectUrl;
        if (file) {
            try {
                objectUrl = URL.createObjectURL(file);
                setImageSrc(objectUrl);
            } catch (error) {
                console.error("Error creating object URL:", error);
            }
        } else if (url) {
            setImageSrc(url);
        }

        return () => {
            if (objectUrl) {
                URL.revokeObjectURL(objectUrl);
            }
        };
    }, [file, url]);

    return (
        <div className='relative h-48'>
            {imageSrc && (
                <div className='relative h-full'>
                    <div className="flex h-full items-center justify-center">
                        <img src={imageSrc} alt="Изображение" className='w-full h-full object-cover  rounded-lg' />
                    </div>
                    <ImageRemoveButton onClick={onRemove}>Удалить</ImageRemoveButton>
                </div>
            )}
        </div>
    );
};

const ImageRemoveButton = styled(Button)(({ theme }) => ({
    position: 'absolute',
    top: 0,
    right: 0,
    fontSize: '10px',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    color: '#fff',
    minWidth: 'auto',
    padding: '4px',
    '&:hover': {
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
    },
}));

export default ImageUploadInput;
