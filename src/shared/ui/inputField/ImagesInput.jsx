import { useRef } from 'react';

const InputImage = ({
    name,
    accept = '',
    label,
    id,
    onImageUpload,
    style
}) => {
    const ref = useRef(null);
    return (
        <>
            <label title="" htmlFor={id}
                className={`${style} rounded border-2 cursor-pointer`}>
                <div className="relative">
                    <span className="block text-base text-center font-semibold relative text-black group-hover:text-blue-500 p-5">
                        {label}
                    </span>
                </div>
            </label>
            <input
                id={id}
                name={name}
                onChange={onImageUpload}
                type="file"
                className="hidden"
                accept={accept}
                ref={ref}
                multiple
            />

        </>
    );
};

export default InputImage;
