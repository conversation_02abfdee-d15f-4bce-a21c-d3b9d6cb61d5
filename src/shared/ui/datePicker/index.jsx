import React from 'react';
import './custom-datepicker.css';
import { ru } from 'date-fns/locale';
import { DateRange } from 'react-date-range';
import 'react-date-range/dist/styles.css'; // основной css файл
import 'react-date-range/dist/theme/default.css';
import { useOutsideClick } from "../../hooks/useOutsideClick.js";
import useResponsiveMonths from "../../utils/dateRangeResponsive/index.js";

const customLocale = {
    ...ru,
    localize: {
        ...ru.localize,
        day: (n) => ['вс', 'пн', 'вт', 'ср', 'чт', 'пт', 'сб'][n],
    },
};

const DatePicker = ({ onStartDateChange, onEndDateChange, checkInDate, checkOutDate, calendar, t, room }) => {
    const calendarIcon = "/calendarIcon.svg"; // The path to the icon should be without /public
    const [showCalendar, setShowCalendar] = React.useState(false); // состояние для отображения календаря
    const [isSelectingEndDate, setIsSelectingEndDate] = React.useState(false);

    const checkOutTime = room.CheckOut?.Name || "00:00";
    const [hours, minutes] = checkOutTime.split(':').map(Number);

    const calendarRef = React.useRef(null);
    useOutsideClick(calendarRef, () => setShowCalendar(false), showCalendar);
    const monthsToShow = useResponsiveMonths(2, 800);

    const disabledDates = calendar ? calendar.flatMap(({ StartDate, EndDate }) => {
        const dates = [];
        let currentDate = new Date(StartDate);
        const endDate = new Date(EndDate);

        while (currentDate < endDate) {
            dates.push(new Date(currentDate));
            currentDate.setDate(currentDate.getDate() + 1);
        }

        if (hours === 0 && minutes === 0) {
            dates.push(endDate);
        }

        return dates;
    }) : [];

    const handleSelect = (ranges) => {
        let { startDate, endDate } = ranges.selection;

        if (startDate) {
            startDate.setHours(0, 0, 0, 0);
        }

        if (endDate) {
            endDate.setHours(0, 0, 0, 0);
        }

        if (startDate && endDate && startDate.getTime() === endDate.getTime()) {
            endDate = null;
        }

        onStartDateChange(startDate);
        onEndDateChange(endDate);
        if (!endDate) {
            setIsSelectingEndDate(true);
        } else {
            setIsSelectingEndDate(false);
            setShowCalendar(false); // Закрываем календарь, если обе даты выбраны
        }
    };



    const handleInputClick = (e) => {
        e.stopPropagation();
        setShowCalendar(!showCalendar);
    };

    return (
        <div className='relative'>
            <div className="flex flex-col xl:flex-row xl:gap-4">
                <div className="mb-4 relative w-full ">
                    {/* Поля ввода для отображения выбранных дат */}
                    <div className="flex flex-col xl:flex-row gap-2">
                        <div className='relative '>
                            <label
                                className="block text-gray-700 font-semibold mb-2">{t('datePicker.arrivalDate')}</label>
                            <input
                                type="text"
                                value={checkInDate ? checkInDate.toLocaleDateString() : ''}
                                readOnly
                                className="w-full xl:w-[180px] border rounded px-3 py-1.5 cursor-pointer"
                                onClick={handleInputClick}
                                placeholder={checkInDate ? checkInDate.toLocaleDateString() : t('datePicker.arrivalDate')}
                            />
                            <img
                                src={calendarIcon}
                                alt="calendar icon"
                                className="absolute right-3 top-10 w-5 h-5 pointer-events-none"
                            />
                        </div>
                        <div className='relative '>
                            <label
                                className="block text-gray-700 font-semibold mb-2">{t('datePicker.departureDate')}</label>
                            <input
                                type="text"
                                value={checkOutDate ? checkOutDate.toLocaleDateString() : ''}
                                readOnly
                                className="w-full xl:w-[180px]  border rounded px-3 py-1.5 cursor-pointer"
                                onClick={handleInputClick}
                                placeholder={checkOutDate ? checkOutDate.toLocaleDateString() : t('datePicker.departureDate')}
                            />
                            <img
                                src={calendarIcon}
                                alt="calendar icon"
                                className="absolute right-3 top-10 w-5 h-5 pointer-events-none"
                            />
                        </div>
                    </div>

                    {/* Календарь, который появляется при клике */}
                    {showCalendar && (
                        <div className="absolute top-full lg:right-0 xl:-right-5 z-50 mt-2" ref={calendarRef}>
                            <DateRange
                                months={monthsToShow} // Display two months
                                direction="horizontal"
                                ranges={[{
                                    startDate: checkInDate || new Date(), // Начальная дата или сегодняшняя, если не выбрано
                                    endDate: checkOutDate || checkInDate || new Date(),  // Конечная дата = дата заезда, если дата выезда не выбрана
                                    key: 'selection',
                                }]}
                                onChange={handleSelect}
                                minDate={new Date()}
                                disabledDates={disabledDates}
                                locale={customLocale} // Локализация
                                showDateDisplay={false}
                            />
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default DatePicker;
