/* Hide month and year dropdown */
.rdrYearPicker {
    display: none;
}

.rdrCalendarWrapper{
    -webkit-box-shadow: 4px 4px 8px 0px rgba(34, 60, 80, 0.2);
    -moz-box-shadow: 4px 4px 8px 0px rgba(34, 60, 80, 0.2);
    box-shadow: 4px 4px 8px 0px rgba(34, 60, 80, 0.2);
    border-radius: 10px;
    width: 100%;
}

.date-range-wrapper .rdrDayHovered .rdrDayNumber span {
    color: #fcc80a; /* Цвет текста при наведении на дату */
}


.rdrInRange {
    background-color: #949494 !important;
}

.rdrStartEdge, .rdrEndEdge {
    background-color: black !important;
}

.rdrDayNumber span {
    font-size: 16px; /* Укажите нужный размер шрифта */
}


.rdrWeekDays {
    font-size: 14px;
}

/* Customize the width of the calendar */
.rdrMonth,
.rdrCalendarWrapper {
    width: 100%; /* Custom width */
}

/* Responsive styles for mobile */
@media (max-width: 600px) {
    .rdrMonth,
    .rdrCalendarWrapper {
        width: 100%;
        padding: 0;
    }
    .rdrWeekDays {
        margin-top: 24px;
    }
    .rdrDayNumber span {
        font-size: 14px; /* Меньший шрифт для мобильных устройств */
    }
}

@media (max-width: 420px) {
    .rdrMonth,
    .rdrCalendarWrapper {
        width: 80%;
        padding: 0;
    }
    .rdrWeekDays {
        margin-top: 24px;
    }
    .rdrDayNumber span {
        font-size: 14px; /* Меньший шрифт для мобильных устройств */
    }
}

