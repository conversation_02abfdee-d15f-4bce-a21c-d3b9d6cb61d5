const SkeletonLoader = () => {
    return (
        <div className="flex space-x-10 p-5">
            {[...Array(7)].map((_, index) => (
                <div key={index} className="flex flex-col items-center">
                    <div className="h-10 w-10 bg-gray-300 rounded-full animate-pulse" />
                    <div className="mt-2 h-4 w-20 bg-gray-300 rounded-md animate-pulse" />
                </div>
            ))}
        </div>
    );
};

export default SkeletonLoader