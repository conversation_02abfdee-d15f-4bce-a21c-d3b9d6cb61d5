import React from 'react';

const SkeletonMainCard = () => {
    return (
        <div className="animate-pulse">
            {/* Заглушка для изображения */}
            <div className="bg-gray-300 h-48 w-full mb-4"></div>

            {/* Заглушка для цены и рейтинга */}
            <div className="flex justify-between">
                <div className="bg-gray-300 h-6 w-24 mb-4"></div>
                <div className="bg-gray-300 h-6 w-16 mb-4"></div>
            </div>

            {/* Заглушка для заголовка */}
            <div className="bg-gray-300 h-6 w-3/4 mb-4"></div>

            {/* Заглушка для местоположения */}
            <div className="bg-gray-300 h-4 w-1/2 mb-4"></div>

            {/* Заглушка для текста */}
            <div className="bg-gray-300 h-4 w-full mb-4"></div>
            <div className="bg-gray-300 h-4 w-5/6 mb-4"></div>
        </div>
    );
};

export default SkeletonMainCard;
