import React from 'react';

function DetailPageSkeleton() {
    return (
        <div className='flex flex-col md:flex-row gap-10 w-full py-10'>
            <div className='w-full'>
                <div className="animate-pulse">
                    {/* Галерея для больших экранов */}
                    <div
                        className="hidden md:grid md:grid-cols-4 md:grid-rows-2 gap-2.5 mb-4 max-h-[400px] overflow-hidden">
                        <div className="col-span-1 row-span-1 bg-gray-300 h-40 w-full rounded-lg"></div>
                        <div className="col-span-2 row-span-2 bg-gray-300 h-80 w-full rounded-lg"></div>
                        <div className="col-span-1 row-span-1 bg-gray-300 h-40 w-full rounded-lg"></div>
                        <div className="col-span-1 row-span-1 bg-gray-300 h-40 w-full rounded-lg"></div>
                        <div className="col-span-1 row-span-1 bg-gray-300 h-40 w-full rounded-lg"></div>
                    </div>

                    {/* Галерея для мобильных экранов */}
                    <div className="block md:hidden">
                        <div className="relative w-full h-full rounded-lg">
                            <div className="relative h-[300px] sm:h-80 bg-gray-300 w-full rounded-2xl"></div>
                        </div>
                    </div>
                </div>
                <div className="flex flex-col space-y-5 mt-5 md:mt-0 animate-pulse">
                    <div className='space-y-2'>
                        {/* Заголовок и рейтинг */}
                        <div className='flex justify-between'>
                            <div className="bg-gray-300 h-8 w-1/2 rounded"></div>
                            <div className="bg-gray-300 h-8 w-16 rounded-3xl"></div>
                        </div>

                        {/* Локация */}
                        <div className='flex gap-1 items-center'>
                            <div className="bg-gray-300 h-4 w-4 rounded-full"></div>
                            <div className="bg-gray-300 h-4 w-1/4 rounded"></div>
                        </div>

                        {/* Комнаты и кровати */}
                        <div className='flex gap-2'>
                            <div className="flex gap-2 items-center text-gray-700">
                                <div className="bg-gray-300 h-10 w-10 rounded-full"></div>
                                <div className="bg-gray-300 h-4 w-16 rounded"></div>
                            </div>
                            <div className="flex gap-2 items-center text-gray-700">
                                <div className="bg-gray-300 h-10 w-10 rounded-full"></div>
                                <div className="bg-gray-300 h-4 w-16 rounded"></div>
                            </div>
                        </div>
                    </div>
                    {/* Описание */}
                    <div className="flex flex-col w-full space-y-2">
                        <div className="bg-gray-300 h-4 w-1/4 rounded"></div>
                        <div className="bg-gray-300 h-20 w-full rounded"></div>
                    </div>

                    {/* Владелец */}
                    <div className='flex flex-col gap-2 '>
                        <div className="bg-gray-300 h-4 w-1/6 rounded"></div>
                        <div className='flex items-center gap-4'>
                            <div className="bg-gray-300 h-20 w-20 rounded-full"></div>
                            <div>
                                <div className="bg-gray-300 h-4 w-1/2 rounded"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div className='border p-5 shadow-md rounded-2xl animate-pulse w-1/2'>
                {/* Заголовок цены */}
                <div className='flex mb-4 items-center gap-3'>
                    <div className="bg-gray-300 h-8 w-1/3 rounded"></div>
                    <div className="bg-gray-300 h-6 w-1/4 rounded"></div>
                </div>

                {/* Дата заезда и выезда */}
                <div className="mb-4">
                    <div className="bg-gray-300 h-12 w-full rounded"></div>
                </div>

                {/* Количество взрослых */}
                <div className="mb-4">
                    <div className="bg-gray-300 h-4 w-1/4 rounded mb-2"></div>
                    <div className="bg-gray-300 h-10 w-full rounded"></div>
                </div>

                {/* Количество детей */}
                <div className="mb-4">
                    <div className="bg-gray-300 h-4 w-1/4 rounded mb-2"></div>
                    <div className="bg-gray-300 h-10 w-full rounded"></div>
                </div>

                {/* Примечание */}
                <div className="mb-4">
                    <div className="bg-gray-300 h-4 w-1/4 rounded mb-2"></div>
                    <div className="space-y-2">
                        <div className="bg-gray-300 h-4 w-3/4 rounded"></div>
                        <div className="bg-gray-300 h-4 w-2/4 rounded"></div>
                    </div>
                </div>

                {/* Общая стоимость */}
                <div className="flex flex-col my-4 gap-3 border-t pt-4">
                    <div className='flex justify-between'>
                        <div className="bg-gray-300 h-4 w-1/4 rounded"></div>
                        <div className="bg-gray-300 h-4 w-1/4 rounded"></div>
                    </div>
                    <div className='flex justify-between'>
                        <div className="bg-gray-300 h-4 w-1/4 rounded"></div>
                        <div className="bg-gray-300 h-4 w-1/4 rounded"></div>
                    </div>
                </div>

                {/* Кнопка */}
                <div className="bg-gray-300 h-12 w-full rounded"></div>
            </div>
        </div>
    )
        ;
}

export default DetailPageSkeleton;
