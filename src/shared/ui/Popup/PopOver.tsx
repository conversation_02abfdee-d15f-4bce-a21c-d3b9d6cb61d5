import React from 'react'
import { PopoverModule } from '../../../page/public/mainPage/interface'


const PopOver: React.FC<PopoverModule> = ({ children, open, close, className, style }) => {

  const handleOutsideClick = (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    if (event.target === event.currentTarget) {
      close(); 
    }
  }
  return (
    <>
      {open && (
        <div className={`fixed inset-0 flex justify-center bg-blue  z-50 ${className}`} style={style}  onClick={handleOutsideClick}>
          <div className="bg-white p-2 rounded shadow-lg border border-gray-200 relative top-6 rounded-lg">
            <div className='h-full flex flex-col overflow-hidden '>
              {children}
            </div>
          </div>
        </div>
      )}
    </>
  )
}
export default PopOver




