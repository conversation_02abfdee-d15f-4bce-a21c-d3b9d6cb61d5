import React, {useEffect, useState} from 'react';
import { useTranslation } from 'react-i18next';
import { IoIosArrowUp  } from "react-icons/io";
import {useDispatch} from "react-redux";
import {handbookApi} from "../../../store/slices/handbook/handbookSlice.js";

const LanguageSwitcher = () => {
    const { i18n, t } = useTranslation();
    const [isOpen, setIsOpen] = useState(false);

    const changeLanguage = (lng) => {
        i18n.changeLanguage(lng);
        setIsOpen(false); // Закрываем dropdown после выбора языка
    };
    const dispatch = useDispatch();


    useEffect(() => {
        dispatch(handbookApi.util.invalidateTags(['Handbook']));
    }, [i18n.language, dispatch]);

    return (
        <div className="relative inline-block text-left w-max">
            <div
                onClick={() => setIsOpen(!isOpen)}
                className='inline-flex items-center gap-1 justify-center w-full cursor-pointer rounded-md  px-2 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none'>
                <button
                    type="button"
                    className=""

                >
                    {t(`common.${i18n.language}`)}
                </button>
                <div className={`transition-transform duration-300 ${isOpen ? '-rotate-180' : ''}`}>
                    <IoIosArrowUp/>
                </div>
            </div>

            {isOpen && (
                <div
                    className="origin-top-right absolute z-50 right-0 mt-2 w-max rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
                    <div className="py-1" role="menu" aria-orientation="vertical" aria-labelledby="options-menu">
                        <button
                            className={`block w-full text-left px-4 py-2 text-sm ${
                                i18n.language === 'en' ? 'bg-btn-border-blue text-white' : 'text-gray-700'
                            }`}
                            onClick={() => changeLanguage('en')}
                        >
                            {t('common.en')}
                        </button>
                        <button
                            className={`block w-full text-left px-4 py-2 text-sm ${
                                i18n.language === 'ru' ? 'bg-btn-border-blue text-white' : 'text-gray-700'
                            }`}
                            onClick={() => changeLanguage('ru')}
                        >
                            {t('common.ru')}
                        </button>
                        <button
                            className={`block w-full text-left px-4 py-2 text-sm ${
                                i18n.language === 'ky' ? 'bg-btn-border-blue text-white' : 'text-gray-700'
                            }`}
                            onClick={() => changeLanguage('ky')}
                        >
                            {t('common.ky')}
                        </button>
                    </div>
                </div>
            )}
        </div>
    );
};

export default LanguageSwitcher;
