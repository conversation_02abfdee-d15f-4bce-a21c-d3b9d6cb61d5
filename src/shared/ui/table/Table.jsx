import React from 'react';
import Pagination from "../pagination/Pagination.jsx";

const Table = ({
                   columns,
                   data,
                   totalItems,
                   itemsPerPage,
                   currentPage,
                   onPageChange,
}) => {
    const totalPages = Math.ceil(totalItems / itemsPerPage);


    const renderCell = (column, row, index) => {
        const value = column.field
            ? column.field.split('.').reduce((obj, key) => obj && obj[key], row)
            : null;

        if (column.cell) {
            return column.cell(row, index);
        }

        if (column.field === 'Icon' && value) {  // Проверяем, что это поле `icon`
            return <img src={value} alt="icon" className="w-8 h-8 object-contain" />;
        }

        if (column.field === 'actions') {
            return (
                <div className="flex items-center justify-end gap-4">
                    <button
                        className="text-blue-500  flex items-center w-[25px] h-[25px] justify-center hover:text-blue-700"
                        onClick={() => column.handleEdit(row)}
                    >
                        <img  alt="edit" src="/editIcon.svg" />
                    </button>
                    <button
                        className="text-red-500 flex w-[25px] h-[25px] items-center justify-center hover:text-red-700"
                        onClick={() => column.handleDelete(row)}
                    >
                        <img  alt="delete" src="/deleteIcon.svg" />
                    </button>
                </div>
            );
        }

        if (column.field === "CreatedAt" && value) {
            const date = new Date(value);
            return `${date.toLocaleDateString()} ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
        }

        return value || '';
    };


    return (
        <div className="py-4 w-full">
            <div className="hidden md:block ">
                <table className="min-w-full bg-white shadow-lg border-b mb-4 table-fixed rounded-lg overflow-hidden">
                    <thead className='bg-gray-300 w-full '>
                    <tr className="w-full text-left border-b ">
                        {columns.map((column, index) => (
                            <th key={index} className="px-4 py-2"
                                    style={{width: column.width}}>
                                    {column.headerName}
                                </th>
                            ))}
                        </tr>
                    </thead>

                    <tbody>
                    {data && data.length > 0 ? (
                        data.map((row, rowIndex) => (
                            <tr key={rowIndex} className="border-b font-medium">
                                {columns.map((column, colIndex) => (
                                    <td
                                        key={colIndex}
                                        className={`px-4 py-3 text-sm ${
                                            column.field === "edit" || column.field === "delete" ? "px-0" : ""
                                        }`}
                                        style={{ width: column.width }}
                                    >
                                        {renderCell(column, row, rowIndex)}
                                    </td>
                                ))}
                            </tr>
                        ))
                    ) : (
                        <tr>
                            <td colSpan={columns.length + 1} className="text-center py-4">
                                Нет данных для отображения
                            </td>
                        </tr>
                    )}
                    </tbody>
                </table>
            </div>

            <Pagination
                totalPages={totalPages}
                currentPage={currentPage}
                onPageChange={onPageChange}
            />
        </div>
    );
};

export default Table;
