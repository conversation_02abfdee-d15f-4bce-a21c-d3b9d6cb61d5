import { RxCross1 } from "react-icons/rx";
function Modal({
                   isOpen,
                   onClose,
                   onConfirm,
                   title,
                   message,
                   confirmText = 'Продолжить',
                   cancelText = 'Отмена',
                   children,
                   style,
                   buttonHidde = true,
                   confirmButtonProps = {},
                   crossClose=false
               }) {
    if (!isOpen) return null;

    console.log('123')


    return (
        <div className="fixed z-50 inset-0 bg-black bg-opacity-50 flex items-center justify-center">
            <div className={`${style} bg-white mx-10 md:m-0 p-4 sm:p-6 rounded shadow-lg relative`}>
                {crossClose &&
                    <span className='absolute top-2 right-2 p-1 cursor-pointer hover:bg-gray-200 hover:rounded-full ' onClick={onClose}>
                        <RxCross1/>
                   </span>
                }
                <h2 className="text-lg sm:text-lg font-bold mb-2 sm:mb-4 text-center">{title}</h2>
                <p className="mb-4 text-center">{message}</p>
                {children}
                {
                    buttonHidde && (
                        <div className="flex justify-center gap-5">
                            <button className="w-max border px-5 py-2 text-red-500 hover:bg-text-light-red hover:text-white font-semibold rounded-md"
                                    onClick={onClose}>{cancelText}</button>
                            <button
                                onClick={onConfirm}
                                {...confirmButtonProps}
                            >
                                {confirmText}
                            </button>
                        </div>
                    )
                }
            </div>
        </div>
    );
}

export default Modal;
