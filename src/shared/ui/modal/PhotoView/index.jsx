import Button from "../../button/Button.jsx";

function PhotoView({preview,handleCancel,handleUpload }) {
    return (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-10">
            <div className="bg-white p-4 rounded-md">
                <h2 className="text-xl mb-4 font-semibold text-center">Предпросмотр</h2>
                <div className='flex justify-center'>
                    <img
                        src={preview}
                        alt="Preview"
                        className="rounded-full w-[120px] h-[120px] md:w-80 md:h-80  object-cover mb-4"
                    />
                </div>
                <div className="flex justify-around gap-2">
                    <Button onClick={handleCancel} name='Отмена'/>
                    <Button onClick={handleUpload} style='bg-blue-500 text-white' name='Сохранить'/>

                </div>
            </div>
        </div>
    );
}

export default PhotoView;