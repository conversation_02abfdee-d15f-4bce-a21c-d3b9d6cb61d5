import React, { useState } from 'react'
import { SliderPropsWithData } from '../../../page/public/mainPage/interface';
import HeartButton from 'page/public/mainPage/ListData/HeartButton';
import {API_IMG} from "../../config/api";
import noImg from "/public/noImg.jpg"
import {Link} from "react-router-dom";

const Slider: ({images, ImageByBool, style, items, link}: {
    images: any;
    ImageByBool: any;
    style: any;
    items: any;
    link: any
}) => React.JSX.Element = ({ images, ImageByBool, style, items,link  }) => {

    const [currentSlide, setCurrentSlide] = useState<number>(0);
    const numToShow = ImageByBool ? 1 : 10;
    const totalItems = ImageByBool ? images.length : items.length;
    const nextSlide = () => setCurrentSlide(prev => Math.min(prev + numToShow, totalItems - numToShow));
    const prevSlide = () => setCurrentSlide(prev => Math.max(prev - numToShow, 0));


    return (
        <div id="default-carousel" className={`${style} relative group`} data-carousel="slide">
            <div className={`${style} relative overflow-hidden h-full`}>
                {ImageByBool
                    &&
                    <>
                        {(images.length > 0 ? images : [noImg]).slice(currentSlide, currentSlide + numToShow).map((image, index) => (
                            <div key={index} className="relative rounded-xl w-100 h-60 overflow-hidden ">
                                <Link to={link}>
                                    <img
                                        src={images.length > 0 ? API_IMG + image : noImg}
                                        alt="img"
                                        className="rounded-xl w-full h-full object-cover"
                                    />
                                </Link>
                            </div>
                        ))}
                    </>
                }
            </div>
            {currentSlide > 0 && (
                <button type="button"
                        className={`absolute top-0 start-0 z-10 flex items-center justify-center h-full ${!ImageByBool ? 'px-0' : 'px-4'} cursor-pointer ${ImageByBool ? 'group-hover:flex hidden' : 'flex'} focus:outline-none`}
                        data-carousel-prev
                        onClick={(e) => {
                            e.stopPropagation();
                            prevSlide();
                        }}>
                        <span className="inline-flex items-center justify-center w-10 h-10 rounded-full bg-white/30 dark:bg-white group-hover:bg-white/50 dark:group-hover:bg-white group-focus:ring-4 group-focus:ring-white dark:group-focus group-focus:outline-none">
                            <svg className="w-4 h-4 text-white dark:text-gray-800 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 1 1 5l4 4" />
                            </svg>
                        </span>
                </button>
            )}
            {currentSlide + numToShow < totalItems && (
                <button type="button"
                        className={`absolute top-0 end-0 z-10 flex items-center justify-center h-full px-4 cursor-pointer ${ImageByBool ? 'group-hover:flex hidden' : 'flex'} focus:outline-none`}
                        data-carousel-next
                        onClick={(e) => {
                            e.stopPropagation();
                            nextSlide();
                        }}>
                    <span className="inline-flex items-center justify-center w-10 h-10 rounded-full bg-white dark:bg-white group-hover:bg-white/50 dark:group-hover:bg-white group-focus:ring-4 group-focus:ring-white dark:group-focus group-focus:outline-none">
                        <svg className="w-4 h-4 text-white dark:text-gray-800 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4" />
                        </svg>
                    </span>
                </button>
            )}
        </div>
    );
};

export default Slider


