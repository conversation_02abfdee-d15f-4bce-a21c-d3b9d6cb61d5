import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import HttpApi from 'i18next-http-backend';
import LanguageDetector from 'i18next-browser-languagedetector';

i18n
    .use(HttpApi) // Подгрузка переводов через HTTP
    .use(LanguageDetector) // Автоматическое определение языка
    .use(initReactI18next) // Инициализация i18next
    .init({
        supportedLngs: ['en', 'ru', 'ky'], // Список поддерживаемых языков
        fallbackLng: 'ru', // Язык по умолчанию, если нет перевода
        detection: {
            order: ['querystring', 'cookie', 'localStorage', 'navigator', 'htmlTag', 'path', 'subdomain'],
            caches: ['cookie'] // Сохранять выбранный язык в cookie
        },
        backend: {
            loadPath: '/locales/{{lng}}/translation.json', // Путь к файлам переводов
            // Optionally add caching headers for better performance
            requestOptions: {
                cache: 'no-store' // Отключение кэширования на время разработки
            }
        },
        react: {
            useSuspense: false // Отключение suspense для быстрой загрузки
        },
        debug: false, // Включить отладочные сообщения для разработки
    });

export default i18n;
