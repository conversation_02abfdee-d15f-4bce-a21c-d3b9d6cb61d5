/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        'button-red':'#FE3C5F',
        'code-bg': '#fde68a',
        'main-text': '#a21caf',
        'border-bottom': '#594BFF',
        'blue-lock': '#3E5DFF',
        'blue-text-lock': '#1C2863',
        'light-text': '#1C2863',
        'lock-orange': '#FD8626',
        'btn-blue': '#4B5DFF',
        'text-medium': '#3E5DFF',
        'text-light-red': '#F35B05',
        'btn-bg-red': '#FE3C5F',
        'btn-border-blue': '#4B5DFF',
        'back-icon': '#E2E7FF'
      },
      display: ['responsive'],
      gap: ['responsive'],
      flexDirection: ['responsive'],
      screens: {
        'xs': '425px',
      },
      animation: {
        synchronized: 'pulseEffect 2s infinite',
      },
      keyframes: {
        pulseEffect: {
          '0%': { transform: 'scale(1)', opacity: 1 },
          '50%': { opacity: 0.5 },
          '100%': { transform: 'scale(1.2)', opacity: 0 },
        },
      },
    },
  },
  plugins: [],
}