{
    "compilerOptions": {
        "target": "ESNext", // Target the latest ECMAScript version (ESNext)
        "module": "ESNext", // Use ESNext module system
        "types": [
            "vite/client" // Include Vite types
        ],
        "useDefineForClassFields": true,
        "lib": [
            "DOM",
            "DOM.Iterable",
            "ESNext"
        ],
        "allowJs": true, // Allow JavaScript files to be compiled
        "skipLibCheck": true, // Skip type checking of declaration files
        "esModuleInterop": true, // Enable support for ES module imports
        "allowSyntheticDefaultImports": true, // Allow default imports from modules without default exports
        "strict": true, // Enable all strict type-checking options
        "forceConsistentCasingInFileNames": true, // Ensure consistent casing in file names
        "moduleResolution": "Node", // Resolve modules using Node.js style
        "resolveJsonModule": true, // Enable importing JSON files
        "isolatedModules": true, // Ensure every file can be safely transpiled without depending on others
        "noEmit": true, // Do not emit compiled output
        "jsx": "react-jsx", // Use the new JSX transform for React
        "baseUrl": "src" // Set base URL for module resolution
    },
    "include": [
        "src/**/*", // Include all files in the src directory
        "src/**/*.jsx" // Include all JSX files
    ],
    "exclude": [
        "node_modules", // Exclude the node_modules directory
        "dist" // Exclude the dist directory
    ],
    "references": [
        {
            "path": "./tsconfig.node.json" // Reference another tsconfig file
        }
    ]
}
