import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import tsconfigPaths from 'vite-tsconfig-paths'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react(), tsconfigPaths()],
  optimizeDeps: {
    include: ['react-date-range']
  }
  // optimizeDeps: {
    // exclude: ['react-leaflet-search','leaflet','react-leaflet']
  // }
  // server: {
  //   host: '0.0.0.0',
  //   port: 3009
  // }
})
