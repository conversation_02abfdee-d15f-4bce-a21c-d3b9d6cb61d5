{"auth": {"login": "Вход", "email": "Электронная почта", "password": "Пароль", "enterPassword": "Введите пароль", "enterEmail": "Введите электронную почту", "forgotYourPassword": "Забыли пароль", "passwordsDontMatch": "Пароли не совпадают", "dontHaveAccount": "У вас нет аккаунта?", "signGoogle": "Войти с помощью Google", "register": "Регистрация", "confirmYourPassword": "Подтвердите пароль", "doYouHaveAccount": "У вас есть аккаунт?", "passwordRecovery": "Восстановление пароля", "rememberPassword": "Вспомнили пароль?", "sendCodeText": "На вашу почту будет отправлен код для подтверждения", "enterCodeEmail": "Введите код из электронной почты", "verificationCode": "Код подтверждения", "newPassword": "Новый пароль", "enterNewPassword": "Введите новый пароль", "confirmNewPassword": "Подтвердите новый пароль", "nickname": "Имя", "enterNickname": "Введите имя"}, "header": {"myAds": "Мои объявления", "logout": "Выйти", "search": "Поиск", "rentHouse": "Сдать жилье", "login": "Войти"}, "filter": {"filters": "Фильтры", "selectAmenities": "Выберите удобства", "priceRange": "Ценовой диапазон", "byPriceRange": "По ценовому диапазону", "roomsAndBeds": "Комнаты и кровати", "conveniences": "Удобства", "byRating": "По рейтингу", "clearAll": "Очистить все", "apply": "Применить", "minPrice": "Минимальная цена", "maxPrice": "Максимальная цена", "rooms": "Комнаты", "beds": "Кровати"}, "accommodationCard": {"active": "Акти<PERSON><PERSON>н"}, "ownerPage": {"advertisements": "Объявления", "calendar": "Календарь", "applications": "Заявки", "lockManagement": "Управление замками", "add": "Добавить", "yourAdvertisements": "Ваши объявления", "chooseAccommodation": "Выбрать жилье", "noReservation": "Нет бронировании", "selectAccommodation": "Выберите жилье", "noAccommodation": "Нет объектов для отображения"}, "detailPage": {"rooms": "Комнаты", "beds": "кровати", "description": "Описание", "facilitiesTile": "Какие удобства вас ждут", "showAll": "Показать все", "allFacilities": "Все удобства"}, "bookingBar": {"perDay": "за сутки", "numberGuests": "Количество гостей", "numberСhildren": "Количество детей", "note": "Примечание"}, "bookingPage": {"bookingDetails": "Данные о бронировании", "arrival": "Прибытие ", "departure": "Выезд ", "adults": "Взрослых ", "children": "<PERSON><PERSON><PERSON><PERSON><PERSON> ", "fillInformation": "Заполните информацию о себе ", "message": "Сообщение "}, "createAccommodation": {"name": "Название", "category": "Категория", "description": "Описание", "numberOfGuests": "Количество гостей", "numberOfRooms": "Количество комнат", "pricePerDay": "Цена за сутки", "discount": "Скидка", "pricingDescription": "Описание ценообразования", "facilities": "Удобства", "rules": "Правила", "checkInTime": "Время заезда", "checkOutTime": "Время выезда", "country": "Страна", "city": "Город", "streetHouse": "Улица/дом", "uploadPhoto": "Загрузить фото", "addPhoto": "Добавить фото", "createAd": "Создать объявление", "editData": "Изменить данные", "selectMainImage": "Выбрать как основное фото", "mainImage": "Основное фото"}, "applications": {"inbox": "Входящие", "inProgress": "В процессе", "rejected": "Отклоненные", "confirm": "Подтвердить", "reject": "Отклонить", "noApplications": "Нет заявок"}, "rejectedModal": {"reasonForRefusal": "Причина отказа"}, "applicationCard": {"applicationDateAndTime": "Дата и время заявки", "amount": "Сумма", "numberOfDays": "Количество дней", "numberOfGuests": "Количество гостей", "arrivalDate": "Дата заезда", "departureDate": "Дата выезда", "status": "Статус", "client": "Кли<PERSON><PERSON>т", "currency": "сом", "bookingTime": "Время бронирования"}, "status": {"pending": "В ожидании", "active": "Активный", "rejected": "Отклоненный"}, "lockManagement": {"chooseAccommodation": "Выберите жилье", "open": "Открыть", "close": "Закрыть", "key": "<PERSON><PERSON><PERSON><PERSON>", "period": "Период", "client": "Кли<PERSON><PERSON>т", "code": "<PERSON>од", "ownerCode": "Код владельца", "start": "Начало", "end": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "addLock": {"addNewKey": "Добавить новый ключ", "editKey": "Редактирова<PERSON>ь ключ", "accessType": "Тип доступа", "temporary": "Временный", "constant": "Постоянный", "keyName": "Название ключа", "startDateAndTime": "Дата и время начала", "endDateAndTime": "Дата и время окончания", "keyCode": "<PERSON>од ключа"}, "profilePage": {"profile": "Профиль", "account": "Аккаунт", "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myBookings": "Мои бронирования", "settings": "Настройки", "changePassword": "Сменить пароль", "logout": "Выйти", "FillPassportDetails": "Заполните паспортные данные", "message": "Для того чтобы сдать жилье, необходимо заполнить паспортные данные"}, "addPassportInfo": {"passportDetails": "Паспортные данные", "name": "Имя", "surname": "Фамилия", "patronymic": "Отчество", "nationality": "Национальность", "dateOfBirth": "Дата рождения", "documentNumber": "Номер документа", "dateOfIssue": "Дата выдачи", "dateOfExpiry": "Дата истечения срока", "authority": "Орган выдачи", "pin": "ПИН", "enterName": "Введите данные", "enterDocNumber": "Введите номер документа", "enterDateBirth": "Введите дату рождения", "enterNationality": "Введите национальность", "enterAuthority": "Введите орган выдачи", "enterDate": "Введите дату", "enter": "Введите", "modalInfo": "Добавление паспортных данных"}, "account": {"personalInformation": "Личная информация", "name": "Имя", "surName": "Фамилия", "emailAddress": "Электронный адрес", "phoneNumber": "Номер телефона", "edit": "Редактировать", "accountVerified": "Аккаунт верифицирован", "selectDocumentType": "Выберите тип документа", "idCard": "Идентификационная карта", "foreignPassport": "Заграничный паспорт", "photoDocument": "Фото документа"}, "footer": {"aboutUs": "О нас", "profile": "Профиль", "settings": "Настройки", "allRightsReserved": "Все права защищены"}, "userView": {"title": "Информация о владельце", "name": "Имя", "surname": "Фамилия", "contactDetails": "Контактные данные"}, "datePicker": {"selectDate": "Выберите дату", "arrivalDate": "Дата заезда", "departureDate": "Дата выезда"}, "notification": {"seeAll": "Посмотреть все"}, "button": {"confirm": "Подвердить", "reject": "Отклонить", "send": "Отправить", "sendCode": "Отправить код", "open": "Открыть", "close": "Закрыть", "change": "Изменить", "delete": "Удалить", "cancel": "Отмена", "save": "Сохранить", "fillIn": "Заполнить", "order": "Оформить", "book": "Забронировать"}, "placeholder": {"enterKeyCode": "Введите код ключа", "enterKeyName": "Введите название ключа"}, "common": {"or": "или", "active": "Акти<PERSON><PERSON>н", "disabled": "Отключен", "en": "Англ.", "ru": "Ру", "ky": "<PERSON><PERSON>", "total": "Итого", "totalAmount": "Общая сумма", "currencySom": "сом", "day": "сутки", "days": "суток", "please": "Пожалуйста", "register": "зарегистрируйтесь", "singIn": "войдите"}, "role": {"owner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "client": "Кли<PERSON><PERSON>т"}, "modal": {"mustLogin": "Необходимо войти в систему", "toProceedWithBooking": "чтобы продолжить бронирование", "successMessageAddPassport": "Ваши паспортные данные успешно сохранены", "verificationTitle": "Заполните данные", "verificationMessage": "Для бронирования необходимо загрузить фото паспорта", "verificationButton": "Перейти в профиль"}, "moderation": {"title": "Ваша заявка находится на рассмотрении", "message": "Ожидайте ответ в ближайшее время"}, "about": {"title": "О нас", "description": {"p1": "<strong>TogoLock</strong> — это современное решение для краткосрочной и долгосрочной аренды жилья. Мы объединяем арендодателей и арендаторов на одной платформе и предлагаем удобный способ доступа к жилью с помощью умных замков, без личной встречи и обмена ключами.", "p2": "Мы помогаем сдавать, арендовать и продавать жилье быстро и безопасно. Наша цель — сделать этот процесс максимально удобным, прозрачным и надёжным.", "p3": "Благодаря современным технологиям, таким как умные замки, мы обеспечиваем комфорт и безопасность как для владельцев, так и для арендаторов."}, "advantagesTitle": "Наши преимущества", "advantages": {"01": {"title": "Безопасность", "text": "Умные замки с одноразовыми кодами избавляют от необходимости личных встреч для передачи ключей"}, "02": {"title": "Простота", "text": "Быстрая онлайн-бронирование и оплата"}, "03": {"title": "Гибкость", "text": "Можно сдавать посуточно, на долгий срок или продавать"}, "04": {"title": "Контроль", "text": "Владельцы могут отслеживать бронирования и управлять доступом через платформу"}}}}