{"auth": {"login": "Кирүү", "email": "Электрондук почта", "password": "Сыр сөз", "enterPassword": "Сыр сөздү киргизиңиз", "enterEmail": "Электрондук почтаны киргизиңиз", "forgotYourPassword": "Сыр сөздү унуттуңузбу?", "passwordsDontMatch": "Сырсөздөр бирдей эмес", "dontHaveAccount": "Аккаунтуңуз жокпу?", "signGoogle": "Google менен кирүү", "register": "Кат<PERSON><PERSON><PERSON><PERSON>у", "confirmYourPassword": "Сыр сөздү ырастоо", "doYouHaveAccount": "Аккаунтуңуз барбы?", "passwordRecovery": "Сыр сөздү калыбына келтирүү", "rememberPassword": "Сырсөз эсиңиздеби?", "sendCodeText": "Ырастоо үчүн почтаңызга код жөнөтүлөт", "enterCodeEmail": "Электрондук почтадан кодду киргизиңиз", "verificationCode": "Ырастоо коду", "newPassword": "Жаңы сырсөз", "enterNewPassword": "Жаңы cыр сөздү киргизиңиз", "confirmNewPassword": "Жаңы cыр сөздү ырастаңыз", "nickname": "Атыныз", "enterNickname": "Атынызды киргизиңиз"}, "header": {"myAds": "<PERSON><PERSON><PERSON><PERSON><PERSON> жарнамаларым", "logout": "Чыг<PERSON>у", "search": "Издөө", "rentHouse": "Турак-жайды ижарага берүү", "login": "Кирүү"}, "filter": {"filters": "Фильтрлер", "selectAmenities": "Ыңгайлуулуктарды тандаңыз", "priceRange": "Баанын диапазону", "byPriceRange": "Баанын диапазону боюнча", "roomsAndBeds": "Бөлмөлөр жана керебеттер", "conveniences": "Ыңгайлуулуктар", "byRating": "Рейтинг боюнча", "clearAll": "Баа<PERSON><PERSON>н тазалоо", "apply": "Колдонуу", "minPrice": "Минималдуу баа", "maxPrice": "Максималдуу баа", "rooms": "Бөлмөлөр", "beds": "Ке<PERSON>ебеттер"}, "accommodationCard": {"active": "Активдүү"}, "ownerPage": {"advertisements": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "calendar": "Календарь", "applications": "Билдирүүлөр", "lockManagement": "Кулпуну башкаруу", "add": "Ко<PERSON><PERSON><PERSON>", "yourAdvertisements": "Сиздин жарнамаларыңыз", "chooseAccommodation": "Турак жайды тандоо", "noReservation": "Ээлөөлөр жок", "selectAccommodation": "Турак жайды тандаңыз", "noAccommodation": "Көрсөтүү үчүн объекттер жок"}, "detailPage": {"rooms": "Бөлмөлөр", "beds": "Ке<PERSON>ебеттер", "description": "Баяндоо", "facilitiesTile": "Кандай ыңгайлуулуктар күтүлөт", "showAll": "Баарын көрсөтүү", "allFacilities": ""}, "bookingBar": {"perDay": "күнүнө", "numberGuests": "Коноктордун саны", "numberСhildren": "<PERSON>а<PERSON> балдардын саны", "note": "Эскертүү"}, "bookingPage": {"bookingDetails": "Ээлөөнун маалыматы", "arrival": "Келүү ", "departure": "Кетүү ", "adults": "Чоңдор ", "children": "<PERSON><PERSON><PERSON> балдар ", "fillInformation": "Өзүңүздүн маалыматыңызды толтуруңуз ", "message": "<PERSON><PERSON><PERSON><PERSON><PERSON> "}, "createAccommodation": {"name": "Аты", "category": "Категория", "description": "Баяндоо", "numberOfGuests": "Коноктордун саны", "numberOfRooms": "Бөлмөлөрдүн саны", "pricePerDay": "Күндүк баа", "discount": "Арзанда<PERSON><PERSON>у", "pricingDescription": "Баалоо сүрөттөмөсү", "facilities": "Ыңгайлуулуктар", "rules": "Эре<PERSON><PERSON><PERSON><PERSON>р", "checkInTime": "Кирүү убактысы", "checkOutTime": "Чыгуу убактысы", "country": "Өлкө", "city": "<PERSON><PERSON><PERSON><PERSON>", "streetHouse": "Көчө/үй", "uploadPhoto": "Сүрөт жүктөө", "addPhoto": "Сүрөт кошуу", "editData": "Маалыматтарды өзгөртүү", "createAd": "Жарнама түзүү", "selectMainImage": "Негизги сүрөт катары тандаңыз", "mainImage": "Негизги сүрөт"}, "applications": {"inbox": "Кирүүчү каттар", "inProgress": "Жүрүп жатат", "rejected": "Четке кагылган", "confirm": "Ырастоо", "reject": "Четке кагуу", "noApplications": "Билдирүүлөр жок"}, "rejectedModal": {"reasonForRefusal": "Четке кагуунун себеби"}, "applicationCard": {"applicationDateAndTime": "Билдирүү күнү жана убактысы", "amount": "Сумма", "numberOfDays": "Күндөрдүн саны", "numberOfGuests": "Коноктордун саны", "arrivalDate": "Келүү күнү", "departureDate": "Кетүү күнү", "status": "Статус", "client": "Кард<PERSON><PERSON>", "currency": "сом", "bookingTime": "Брондоо убактысы"}, "status": {"pending": "Күтүүдө", "active": "Активдүү", "rejected": "Четке кагылган"}, "lockManagement": {"chooseAccommodation": "Турак жайды тандоо", "open": "<PERSON><PERSON><PERSON><PERSON>", "close": "Ж<PERSON><PERSON><PERSON><PERSON>", "key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "period": "Мөөнөт", "client": "Кард<PERSON><PERSON>", "code": "<PERSON>од", "ownerCode": "Ээ<PERSON><PERSON><PERSON><PERSON>н коду", "start": "Баштоо", "end": "Аяктоо"}, "addLock": {"addNewKey": "Жаңы ачкыч кошуу", "editKey": "Ачкычты өзгөртүү", "accessType": "Кирүү түрү", "temporary": "Убактылуу", "constant": "Туруктуу", "keyName": "Ачкычтын аты", "startDateAndTime": "Баштоо күнү жана убактысы", "endDateAndTime": "Аяктоо күнү жана убактысы", "keyCode": "Ачкычтын коду"}, "profilePage": {"profile": "Профиль", "account": "Аккаунт", "owner": "Ээси", "myBookings": "Ме<PERSON><PERSON>н ээлөөлөрүм", "settings": "Орно<PERSON>уулар", "changePassword": "Сыр сөздү өзгөртүү", "logout": "Чыг<PERSON>у", "FillPassportDetails": "Паспорт маалыматтарын толтуруңуз", "message": "Турак жайды ижарага берүү үчүн паспорттук маалыматтарды толтуруу керек"}, "addPassportInfo": {"passportDetails": "Паспорт маалыматы", "name": "Аты", "surname": "Фамилия", "patronymic": "Ата<PERSON><PERSON><PERSON>ын аты", "nationality": "Улуту", "dateOfBirth": "Туулган күнү", "documentNumber": "Документтин номери", "dateOfIssue": "Берилген күнү", "dateOfExpiry": "Мөөнөтүнүн аякташы", "authority": "Берген орган", "pin": "ПИН", "enterName": "Аты-жөнүздү киргизиңиз", "enterDocNumber": "Документтин номерин киргизиңиз", "enterDateBirth": "Туулган күндү киргизиңиз", "enterNationality": "Улутун киргизиңиз", "enterAuthority": "Берген органды киргизиңиз", "enterDate": "Күндү киргизиңиз", "enter": "киргизиңиз", "modalInfo": "Паспорттун маалыматтарын кошуу"}, "account": {"personalInformation": "Жеке маалымат", "name": "Аты", "surName": "Фамилия", "emailAddress": "Электрондук почта дареги", "phoneNumber": "Телефон номери", "edit": "Өзгөртүү", "accountVerified": "Аккаунт текшерилди", "selectDocumentType": "Документтин түрүн тандаңыз", "idCard": "Идентификациялык карта", "foreignPassport": "Чет элдик паспорт", "photoDocument": "Документтин сүрөтү"}, "footer": {"aboutUs": "Биз жөнүндө", "profile": "Профиль", "settings": "Орно<PERSON>уулар", "allRightsReserved": "Бардык укуктар корголгон"}, "userView": {"title": "Ээси жөнүндө маалымат", "name": "Аты", "surname": "Фамилиясы", "contactDetails": "Байланыш маалыматтары"}, "datePicker": {"selectDate": "Күндү тандаңыз", "arrivalDate": "Катталуу күнү", "departureDate": "Чыгуу күнү"}, "notification": {"seeAll": "Баарын көрүү"}, "button": {"confirm": "Ырастоо", "reject": "Четке кагуу", "send": "Жөнөтүү", "sendCode": "Кодду жөнөтүү", "open": "<PERSON><PERSON><PERSON><PERSON>", "close": "Ж<PERSON><PERSON><PERSON><PERSON>", "change": "Өзгөртүү", "delete": "Өчүрүү", "cancel": "Жокко чыгаруу", "save": "Сактоо", "fillIn": "Толтуруу", "order": "Тариздөө", "book": "Ээлөө"}, "placeholder": {"enterKeyCode": "Ач<PERSON>ыч кодун киргизиңиз", "enterKeyName": "Ачкычтын атын киргизиңиз"}, "common": {"or": "же", "active": "Активдүү", "disabled": "Өчүрүлгөн", "en": "Англ.", "ru": "Ру", "ky": "<PERSON><PERSON>", "total": "Жа<PERSON><PERSON>ы", "totalAmount": "Жал<PERSON>ы сумма", "currencySom": "сом", "day": "кун", "days": "кун", "please": "Сур<PERSON><PERSON><PERSON>ч", "register": "катталыңыз", "singIn": "кириңиз"}, "role": {"owner": "Ээси", "client": "Кард<PERSON><PERSON>"}, "modal": {"mustLogin": "Системага кирүү керек", "toProceedWithBooking": "ээлөөнү улантуу үчүн", "successMessageAddPassport": "Паспортуңуздун маалыматтары ийгиликтүү сакталды", "verificationTitle": "Маалыматтарды толтуруңуз", "verificationMessage": "Ээлеп коюу үчүн паспортуңуздун сүрөтүн жүктөшүңүз керек.", "verificationButton": "Профилге өтүңүз"}, "moderation": {"title": "Сиздин арызыңыз каралууда", "message": "Жакын арада жооп күтүңүз"}, "about": {"title": "Биз жөнүндө", "description": {"p1": "<strong>ToGoLock</strong> — Кыргызстанда кыймылсыз мүлктү ижарага берүү, сатуу жана сатып алуу үчүн платформа.", "p2": "Биз турак жайды тез жана коопсуз ижарага берүүгө, ижаралоого жана сатууга жардам беребиз. Максатыбыз — бул процессти мүмкүн болушунча ыңгайлуу, ачык жана ишенимдүү кылуу.", "p3": "Уюлдук кулпулар сыяктуу заманбап технологиялардын жардамы менен биз менчик ээлери жана ижарачылар үчүн ыңгайлуулукту жана коопсуздукту камсыздайбыз."}, "advantagesTitle": "Биздин артыкчылыктар", "advantages": {"01": {"title": "Коопсуздук", "text": "<PERSON>ир жолку коддор менен иштеген акылдуу кулпулар ачкычты берүү үчүн жолугушуунун кереги жок кылат"}, "02": {"title": "Жөнөкөйлүк", "text": "Онлайн брондоо жана төлөм тез жана жеңил"}, "03": {"title": "Ийкемдүүлүк", "text": "Турак жайды күнүмдүк, узак мөөнөттүү же сатуучу катары сунуштасаңыз болот"}, "04": {"title": "Байкоо жана башкаруу", "text": "Менчик ээлери платформанын жардамы менен брондоолорду көзөмөлдөп, кирүүнү башкара алышат"}}}}