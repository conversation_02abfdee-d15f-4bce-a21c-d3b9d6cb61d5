{"auth": {"login": "<PERSON><PERSON>", "email": "E-mail", "password": "Password", "enterPassword": "Enter password", "enterEmail": "Enter email", "forgotYourPassword": "Forgot your password?", "passwordsDontMatch": "Passwords don't match", "dontHaveAccount": "Don't have an account?", "signGoogle": "Sign in with Google", "register": "Register", "confirmYourPassword": "Confirm your password", "doYouHaveAccount": "Do you have an account?", "passwordRecovery": "Password recovery", "rememberPassword": "Remember your password?", "sendCodeText": "A confirmation code will be sent to your email", "enterCodeEmail": "Enter the code from the email", "verificationCode": "Verification code", "newPassword": "New password", "enterNewPassword": "Enter new password", "confirmNewPassword": "Confirm new password", "nickname": "Nickname"}, "header": {"myAds": "My ads", "logout": "Logout", "search": "Search", "rentHouse": "Rent a house", "login": "<PERSON><PERSON>"}, "filter": {"filters": "filters", "selectAmenities": "Select amenities", "priceRange": "Price range", "byPriceRange": "By price range\n", "roomsAndBeds": "Rooms and beds", "conveniences": "Conveniences", "byRating": "By rating", "clearAll": "Clear all", "apply": "Apply", "minPrice": "Min price", "maxPrice": "Max price", "rooms": "Rooms", "beds": "Beds"}, "accommodationCard": {"active": "Active"}, "ownerPage": {"advertisements": "Advertisements", "calendar": "Calendar", "applications": "Applications", "lockManagement": "Lock management", "add": "Add", "yourAdvertisements": "Your advertisements", "chooseAccommodation": "Choose accommodation", "noReservation": "No reservation", "selectAccommodation": "Select accommodation", "noAccommodation": "No objects to display"}, "detailPage": {"rooms": "Rooms", "beds": "Beds", "description": "Description", "facilitiesTile": "What this place offers", "showAll": "Show all", "allFacilities": "All facilities"}, "bookingBar": {"perDay": "per day", "numberGuests": "Number of guests", "numberСhildren": "Number of children", "note": "Note"}, "bookingPage": {"bookingDetails": "Booking Details", "arrival": "Arrival ", "departure": "Departure ", "adults": "Adults ", "children": "Children ", "fillInformation": "Fill in your information ", "message": "Message "}, "createAccommodation": {"name": "Name", "category": "Category", "description": "Description", "numberOfGuests": "Number of guests", "numberOfRooms": "Number of rooms", "pricePerDay": "Price per day", "discount": "Discount", "pricingDescription": "Pricing description", "facilities": "Facilities", "rules": "Rules", "checkInTime": "Check-in time", "checkOutTime": "Check-out time", "country": "Country", "city": "City", "streetHouse": "Street/house", "uploadPhoto": "Upload photo", "addPhoto": "Add photo", "editData": "Edit", "createAd": "Create an ad", "selectMainImage": "Select as main photo", "mainImage": "Main photo"}, "applications": {"inbox": "Inbox", "inProgress": "In progress", "rejected": "Rejected", "confirm": "Confirm", "reject": "Reject", "noApplications": "No applications"}, "rejectedModal": {"reasonForRefusal": "Reason for refusal"}, "applicationCard": {"applicationDateAndTime": "Application date and time", "amount": "Amount", "numberOfDays": "Number of days", "numberOfGuests": "Number of guests", "arrivalDate": "Arrival date", "departureDate": "Departure date", "status": "Status", "client": "Client", "currency": "som", "bookingTime": "Booking time"}, "status": {"pending": "Pending", "active": "Active", "rejected": "Rejected"}, "lockManagement": {"chooseAccommodation": "Choose accommodation", "open": "Open", "close": "Close", "key": "Key", "period": "Period", "client": "Client", "code": "Code", "ownerCode": "Owner code", "start": "Start", "end": "End"}, "addLock": {"addNewKey": "Add a new key", "editKey": "Edit key", "accessType": "Access type", "temporary": "Temporary", "constant": "Constant", "keyName": "Key name", "startDateAndTime": "Start date and time", "endDateAndTime": "End date and time", "keyCode": "Key code"}, "profilePage": {"profile": "Profile", "account": "Account", "owner": "Owner", "myBookings": "My bookings", "settings": "Settings", "changePassword": "Change Password", "logout": "Logout", "FillPassportDetails": "Fill in your passport details", "message": "In order to rent out housing, you must fill out your passport information"}, "addPassportInfo": {"passportDetails": "Passport details", "name": "First name", "surname": "Last name", "patronymic": "Middle name", "nationality": "Nationality", "dateOfBirth": "Date of birth", "documentNumber": "Document number", "dateOfIssue": "Date of issue", "dateOfExpiry": "Expiry date", "authority": "Issuing authority", "pin": "PIN", "enterName": "Enter details", "enterDocNumber": "Enter document number", "enterDateBirth": "Enter date of birth", "enterNationality": "Enter nationality", "enterAuthority": "Enter issuing authority", "enterDate": "Enter date", "enter": "Enter", "modalInfo": "Adding passport data"}, "account": {"personalInformation": "Personal information", "name": "Name", "surName": "Surname", "emailAddress": "Email address", "phoneNumber": "Phone number", "edit": "Edit", "accountVerified": "Account verified", "selectDocumentType": "Select document type", "idCard": "ID card", "foreignPassport": "Foreign passport", "photoDocument": "Photo of the document"}, "footer": {"aboutUs": "About us", "profile": "Profile", "settings": "Settings", "allRightsReserved": "All rights reserved"}, "userView": {"title": "Owner information", "name": "Name", "surname": "Surname", "contactDetails": "Contact information"}, "datePicker": {"selectDate": "Select date", "arrivalDate": "Arrival date", "departureDate": "Departure date"}, "notification": {"seeAll": "See all"}, "button": {"confirm": "Confirm", "reject": "Reject", "open": "Open", "send": "Send", "sendCode": "Send code", "close": "Close", "change": "Change", "delete": "Delete", "cancel": "Cancel", "save": "Save", "fillIn": "Fill in", "order": "Order", "book": "Booking"}, "placeholder": {"enterKeyCode": "Enter key code", "enterKeyName": "Enter key name"}, "common": {"or": "or", "active": "Active", "disabled": "Disabled", "en": "En", "ru": "<PERSON><PERSON>", "ky": "Kg", "total": "Total", "totalAmount": "Total amount", "currencySom": "som", "day": "day", "days": "days", "please": "Please", "register": "register", "singIn": "sign in"}, "role": {"owner": "Owner", "client": "Client"}, "modal": {"mustLogin": "You must log in", "toProceedWithBooking": "to proceed with booking", "successMessageAddPassport": "Your passport details have been saved successfully", "verificationTitle": "Fill in the details", "verificationMessage": "To make a reservation you must upload a photo of your passport.", "verificationButton": "Go to profile"}, "moderation": {"title": "Your application is under consideration", "message": "Expect a response soon"}, "about": {"title": "About Us", "description": {"p1": "<strong>ToGoLock</strong> is a platform for renting, selling, and buying real estate in Kyrgyzstan.", "p2": "We help you rent out, rent, and sell property quickly and safely. Our goal is to make this process as convenient, transparent, and reliable as possible.", "p3": "Thanks to modern technologies like smart locks, we provide comfort and security for both property owners and renters."}, "advantagesTitle": "Our Advantages", "advantages": {"01": {"title": "Security", "text": "Smart locks with one-time codes eliminate the need for in-person key exchanges"}, "02": {"title": "Simplicity", "text": "Fast online booking and payment"}, "03": {"title": "Flexibility", "text": "You can rent out daily, long-term, or sell your property"}, "04": {"title": "Control", "text": "Owners can track bookings and manage access through the platform"}}}}